{"basic": {"output": {"filename": "Luxury_Home_Tour_Los_Angeles_Simplified", "quality": "standard"}, "audio": {"background": {"music": "https://storage.googleapis.com/pub_content/audios/Cabo_Cantina.mp3", "volume": 0.2}, "narration": {"volume": 1.0}, "music": {"intro": null, "outro": null, "volume": 0.8}, "effects": {"transition": null, "click": null, "success": null}}}, "intro": {"duration": 3.0, "title": {"text": "Luxury Home Tour", "style": {"font_size": 96, "color": "#FFD700", "position": "center", "margin_top": -80}}, "subtitle": {"text": "Los Angeles · 4BR/4BA · Gated Estate", "style": {"font_size": 44, "color": "#FFFFFF", "position": "center", "margin_top": 80}}, "background": {"type": "color", "value": "#000000"}, "animation": {"text_effect": "fade_in", "transition_out": "fade"}}, "segments": [{"id": "8f4e2b5d-1a7c-4e9f-b3d8-5c2a7e9f4b1e", "title": "Overview", "scenes": [{"id": "a1b2c3d4-5e6f-7890-abcd-ef1234567890", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/14b297fd-ffd2-4740-9dbc-1bb8fa2deb16.mp3", "duration": 3.47}, "caption": {"text": "Welcome to a glimpse of luxury living in Los Angeles.", "style": {"font_size": 42, "color": "white", "stroke_color": "black", "stroke_width": 2}, "animation": {"type": "fade_in", "fade_in_duration": 0.5, "fade_out_duration": 0.3}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/a5fd85e5-b5f0-4842-9824-26d1bc803caf.jpg", "animation": {"type": "auto", "intensity": 1.0}}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/7fda1b05-9d2b-44a1-8b6e-8bca7281feca.mp4", "position": "bottom_left", "style": {"is_circular": true, "background_color": "#FFFFFF"}}, "overlays": [{"id": "f8e7d6c5-b4a3-9281-7065-4e3b2a1f9c8e", "type": "text", "content": "Amor Road, 90046", "position": "top_left", "offset": {"x": 50, "y": 50}, "style": {"font_size": 32, "color": "#FFD700", "background": "rgba(0,0,0,0.8)", "padding": 15, "border_radius": 8}, "timing": {"start": 0.5, "duration": 2.5}, "animation": {"entrance": "slide_down", "exit": "fade_out"}}]}, {"id": "b7c9d2e4-f6a8-1b3c-5d7e-9f0a2b4c6d8e", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/6d82d05f-8776-4310-b1fe-3acfc46f4751.mp3", "duration": 5.98}, "caption": {"text": "We're touring a stunning property located on Amor Road in the desirable 90046 zip code.", "style": {"font_size": 42, "color": "white", "stroke_color": "black", "stroke_width": 2}, "animation": {"type": "fade_in", "fade_in_duration": 0.5, "fade_out_duration": 0.3}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/4ac2ec9f-4868-4d33-9c03-fa757fc215bf.jpg", "animation": {"type": "auto", "intensity": 1.0}}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/0d45bea2-cd65-4944-8403-d33472fce802.mp4", "position": "bottom_left", "style": {"is_circular": true, "background_color": "#FFFFFF"}}}, {"id": "c8d9e0f1-a2b3-4c5d-6e7f-8909a1b2c3d4", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/4c1234b3-6b15-4e3c-8ddf-474d71d8cf63.mp3", "duration": 4.96}, "caption": {"text": "This 4-bedroom, 4-bathroom home offers the ultimate in privacy and elegance.", "style": {"font_size": 42, "color": "white", "stroke_color": "black", "stroke_width": 2}, "animation": {"type": "fade_in", "fade_in_duration": 0.5, "fade_out_duration": 0.3}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/a5fd85e5-b5f0-4842-9824-26d1bc803caf.jpg", "animation": {"type": "zoom_in", "intensity": 0.8}}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/7d76f9ec-0c13-4377-bd9b-5a8921421549.mp4", "position": "bottom_left", "style": {"is_circular": true, "background_color": "#FFFFFF"}}}]}, {"id": "e3f5a7b9-c1d2-4e6f-8a9b-2c4d6e8f0a1b", "title": "Interior and Features", "scenes": [{"id": "d9e8f7a6-b5c4-3d2e-1f0a-9b8c7d6e5f4a", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/989bc332-2eb7-4059-ba1d-5956a077c65d.mp3", "duration": 4.36}, "caption": {"text": "The architecture boasts a seamless flow between indoor and outdoor spaces.", "style": {"font_size": 42, "color": "white", "stroke_color": "black", "stroke_width": 2}, "animation": {"type": "fade_in", "fade_in_duration": 0.5, "fade_out_duration": 0.3}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/5f6a5a64-9340-464f-a3fd-077b70fbbc78.jpg", "animation": {"type": "zoom_out", "intensity": 0.8}}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/68045164-fe25-4050-b9b7-e3b153b9af0a.mp4", "position": "bottom_left", "style": {"is_circular": true, "background_color": "#FFFFFF"}}}, {"id": "e0f1a2b3-c4d5-6e7f-8901-2a3b4c5d6e7f", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/5c727cd5-4d4d-468d-8bef-71e90ccef330.mp3", "duration": 4.36}, "caption": {"text": "Enjoy an infinity pool with breathtaking canyon views from almost every room.", "style": {"font_size": 42, "color": "white", "stroke_color": "black", "stroke_width": 2}, "animation": {"type": "fade_in", "fade_in_duration": 0.5, "fade_out_duration": 0.3}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/5b1a74cb-59ce-464c-b8fa-ad0faae715db.jpg", "animation": {"type": "zoom_in", "intensity": 1.0}}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/dec911ae-8bce-4598-9780-b9ca9d70f72d.mp4", "position": "bottom_left", "style": {"is_circular": true, "background_color": "#FFFFFF"}}}, {"id": "f1a2b3c4-d5e6-7f80-9012-3a4b5c6d7e8f", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/7c40f1a0-e731-4319-9d8b-e2f8b066216e.mp3", "duration": 4.96}, "caption": {"text": "High-end finishes and thoughtful design details elevate the living experience.", "style": {"font_size": 42, "color": "white", "stroke_color": "black", "stroke_width": 2}, "animation": {"type": "fade_in", "fade_in_duration": 0.5, "fade_out_duration": 0.3}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/5f6a5a64-9340-464f-a3fd-077b70fbbc78.jpg", "animation": {"type": "zoom_out", "intensity": 0.8}}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/7404ea7d-7fac-419d-9b80-50656cec8471.mp4", "position": "bottom_left", "style": {"is_circular": true, "background_color": "#FFFFFF"}}}]}, {"id": "a2b3c4d5-e6f7-8901-2345-6789abcdef01", "title": "Outdoor Oasis", "scenes": [{"id": "b3c4d5e6-f7a8-9012-3456-789abcdef012", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/f885c2e5-20ef-4e47-a808-d134aab99690.mp3", "duration": 6.16}, "caption": {"text": "Step outside to discover lush landscaping and an outdoor space perfect for relaxation and entertainment.", "style": {"font_size": 42, "color": "white", "stroke_color": "black", "stroke_width": 2}, "animation": {"type": "fade_in", "fade_in_duration": 0.5, "fade_out_duration": 0.3}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/43faf7ac-dd7e-4ad2-a94d-4362ba9d9868/0dac4e87-d737-425c-82f0-b0a432099a59.jpg", "animation": {"type": "zoom_in", "intensity": 1.0}}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/1e66ba61-9696-47ec-8d26-24dd2537c79a.mp4", "position": "bottom_left", "style": {"is_circular": true, "background_color": "#FFFFFF"}}}, {"id": "c4d5e6f7-a8b9-0123-4567-89abcdef0123", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/4a0cab57-b61b-4766-8a69-f0303f4c5bec.mp3", "duration": 6.27}, "caption": {"text": "Enjoy a dip in the infinity pool by a two-story waterfall, or relax in either the indoor or outdoor jacuzzi.", "style": {"font_size": 42, "color": "white", "stroke_color": "black", "stroke_width": 2}, "animation": {"type": "fade_in", "fade_in_duration": 0.5, "fade_out_duration": 0.3}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/43faf7ac-dd7e-4ad2-a94d-4362ba9d9868/5d21dfce-e064-43e4-bdb1-cfd859cbff1d.jpg", "animation": {"type": "zoom_out", "intensity": 1.2}}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/6df8b06a-dd40-42b9-94b5-68407df31feb.mp4", "position": "bottom_left", "style": {"is_circular": true, "background_color": "#FFFFFF"}}}]}, {"id": "d5e6f7a8-b9c0-1234-5678-9abcdef01234", "title": "Bedrooms and Privacy", "scenes": [{"id": "e6f7a8b9-c0d1-2345-6789-abcdef012345", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/809f2117-2358-4cc9-8599-bbc4bdc74c31.mp3", "duration": 4.6}, "caption": {"text": "The bedrooms offer serene retreats with divine lighting and exceptional privacy.", "style": {"font_size": 42, "color": "white", "stroke_color": "black", "stroke_width": 2}, "animation": {"type": "fade_in", "fade_in_duration": 0.5, "fade_out_duration": 0.3}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/24c8df6d-7c04-4b5f-b91c-cafbbdf43210.jpg", "animation": {"type": "zoom_in", "intensity": 0.8}}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/aa9f4690-6527-44ab-885b-26c487540c7a.mp4", "position": "bottom_left", "style": {"is_circular": true, "background_color": "#FFFFFF"}}}, {"id": "f7a8b9c0-d1e2-3456-789a-bcdef0123456", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/5018e686-4f34-4e2b-a29c-f1ad4c63cf23.mp3", "duration": 5.62}, "caption": {"text": "This gated hilltop estate provides a tranquil escape with easy access to vibrant city life.", "style": {"font_size": 42, "color": "white", "stroke_color": "black", "stroke_width": 2}, "animation": {"type": "fade_in", "fade_in_duration": 0.5, "fade_out_duration": 0.3}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/24c8df6d-7c04-4b5f-b91c-cafbbdf43210.jpg", "animation": {"type": "zoom_out", "intensity": 1.0}}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/91e989d4-4380-46f6-966c-73ac07d51118.mp4", "position": "bottom_left", "style": {"is_circular": true, "background_color": "#FFFFFF"}}}]}], "outro": {"duration": 3.0, "title": {"text": "Thank You for Watching", "style": {"font_size": 72, "color": "#FFFFFF", "position": "center", "margin_top": -100}}, "subtitle": {"text": "Contact us for private showing · Amor Road, Los Angeles 90046", "style": {"font_size": 36, "color": "#CCCCCC", "position": "center", "margin_top": 80}}, "background": {"type": "color", "value": "#000000"}, "animation": {"text_effect": "slide_up", "transition_in": "fade"}}}