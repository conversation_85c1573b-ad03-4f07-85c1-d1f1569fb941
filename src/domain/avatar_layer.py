#!/usr/bin/env python3
"""
🎭 Reavo Video Creator - 头像层处理模块
Avatar Layer - 智能头像视频定位、缩放、样式和动画处理

支持功能：
- 智能定位：左上、右上、左下、右下、半屏
- 自动缩放：根据画布尺寸自动调整
- 样式效果：圆角、边框、阴影、背景
- 动画效果：淡入淡出、缩放等
"""

import numpy as np
import cv2
from typing import Dict, Any, List, Tuple, Optional, Callable
from moviepy import VideoFileClip, VideoClip, CompositeVideoClip, ColorClip, vfx
from dataclasses import dataclass
from enum import Enum

class AvatarPosition(Enum):
    """头像位置枚举 - 简化版本，只支持底部左右两个位置"""
    BOTTOM_LEFT = "bottom_left"
    BOTTOM_RIGHT = "bottom_right"

class ShapeType(Enum):
    """形状类型枚举"""
    CIRCLE = "circle"
    RECTANGLE = "rectangle"
    ROUNDED_RECTANGLE = "rounded_rectangle"

class AnimationType(Enum):
    """动画类型枚举"""
    SCALE_IN = "scale_in"
    SCALE_OUT = "scale_out"
    # 未来扩展: SLIDE_IN = "slide_in", FADE_IN = "fade_in", etc.

class AvatarSize(Enum):
    """头像尺寸枚举"""
    SMALL = "small"      # 1/6 屏幕
    MEDIUM = "medium"    # 1/4 屏幕
    LARGE = "large"      # 1/3 屏幕
    HALF = "half"        # 1/2 屏幕

@dataclass
class AvatarStyle:
    """头像样式配置"""
    border_width: int = 0
    border_color: str = "#FFFFFF"
    corner_radius: int = 0
    is_circular: bool = False  # 是否为圆形头像
    shadow_blur: int = 0
    shadow_offset: Tuple[int, int] = (0, 0)
    shadow_color: str = "#000000"
    background_color: Optional[str] = None
    opacity: float = 1.0

@dataclass
class AnimationConfig:
    """动画配置数据类"""
    type: AnimationType
    duration: float = 0.5
    easing_function: str = "ease_out_back"
    intensity: float = 1.0
    custom_params: Optional[Dict[str, Any]] = None

@dataclass
class AvatarAnimation:
    """头像动画配置"""
    fade_in_duration: float = 0.5
    fade_out_duration: float = 0.3
    entrance_effect: Optional[str] = None  # "slide_in", "zoom_in", "fade_in"
    exit_effect: Optional[str] = None      # "slide_out", "zoom_out", "fade_out"
    # 新增：明确控制淡入淡出时机
    enable_fade_in: Optional[bool] = None   # None=自动判断, True=强制淡入, False=禁用淡入
    enable_fade_out: Optional[bool] = None  # None=自动判断, True=强制淡出, False=禁用淡出

class AvatarAnimationLibrary:
    """头像动画库"""
    
    def __init__(self):
        """初始化动画库"""
        self.animations = {
            AnimationType.SCALE_IN: self._scale_in_animation,
            AnimationType.SCALE_OUT: self._scale_out_animation,
        }
        
        self.easing_functions = {
            "ease_out_back": self._ease_out_back,
            "ease_in_back": self._ease_in_back,
            "linear": lambda t: t,
            "ease_in_out": lambda t: t * t * (3.0 - 2.0 * t)
        }
    
    def apply_animation(self, clip: VideoClip, config: AnimationConfig) -> VideoClip:
        """应用指定动画"""
        if config.type not in self.animations:
            print(f"⚠️  不支持的动画类型: {config.type}")
            return clip
            
        animation_func = self.animations[config.type]
        easing_func = self.easing_functions.get(config.easing_function, self._ease_out_back)
        
        return animation_func(clip, config.duration, easing_func, config.intensity)
    
    def _scale_in_animation(self, clip, duration, easing_func, intensity):
        """缩放入场动画（重构自原有实现）"""
        def scale_transform(get_frame, t):
            frame = get_frame(t)
            
            if t >= duration:
                return frame
            
            progress = t / duration
            eased_progress = easing_func(progress)
            
            # 从80%缩放到100%
            scale_start = 0.8
            scale_end = 1.0
            scale = scale_start + (scale_end - scale_start) * eased_progress * intensity
            scale = max(0.01, min(2.0, scale))
            
            if scale == 1.0:
                return frame
            
            # 中心缩放实现
            h, w = frame.shape[:2]
            new_h, new_w = int(h * scale), int(w * scale)
            
            if new_h <= 0 or new_w <= 0:
                return frame
            
            # 使用cv2进行缩放
            import cv2
            scaled_frame = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
            
            # 创建与原始尺寸相同的画布
            result_frame = np.zeros_like(frame)
            
            # 计算居中位置
            start_h = (h - new_h) // 2
            start_w = (w - new_w) // 2
            end_h = start_h + new_h
            end_w = start_w + new_w
            
            # 确保不越界
            start_h = max(0, start_h)
            start_w = max(0, start_w)
            end_h = min(h, end_h)
            end_w = min(w, end_w)
            
            # 调整缩放帧的尺寸以匹配可用空间
            available_h = end_h - start_h
            available_w = end_w - start_w
            
            if available_h > 0 and available_w > 0:
                if scaled_frame.shape[0] != available_h or scaled_frame.shape[1] != available_w:
                    scaled_frame = cv2.resize(scaled_frame, (available_w, available_h), interpolation=cv2.INTER_LINEAR)
                
                result_frame[start_h:end_h, start_w:end_w] = scaled_frame
            
            return result_frame
        
        return clip.transform(scale_transform)
    
    def _scale_out_animation(self, clip, duration, easing_func, intensity):
        """缩放退场动画（重构自原有实现）"""
        clip_duration = clip.duration
        fade_start_time = clip_duration - duration
        
        def scale_transform(get_frame, t):
            frame = get_frame(t)
            
            if t < fade_start_time:
                return frame
            
            time_in_fade = t - fade_start_time
            progress = time_in_fade / duration
            eased_progress = easing_func(progress)
            
            # 从100%缩放到0%
            scale = 1.0 - eased_progress * intensity
            scale = max(0.01, min(2.0, scale))
            
            if scale == 1.0:
                return frame
            
            # 中心缩放实现
            h, w = frame.shape[:2]
            new_h, new_w = int(h * scale), int(w * scale)
            
            if new_h <= 0 or new_w <= 0:
                # 创建透明帧
                return np.zeros_like(frame)
            
            # 使用cv2进行缩放
            import cv2
            scaled_frame = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
            
            # 创建与原始尺寸相同的画布
            result_frame = np.zeros_like(frame)
            
            # 计算居中位置
            start_h = (h - new_h) // 2
            start_w = (w - new_w) // 2
            end_h = start_h + new_h
            end_w = start_w + new_w
            
            # 确保不越界
            start_h = max(0, start_h)
            start_w = max(0, start_w)
            end_h = min(h, end_h)
            end_w = min(w, end_w)
            
            # 调整缩放帧的尺寸以匹配可用空间
            available_h = end_h - start_h
            available_w = end_w - start_w
            
            if available_h > 0 and available_w > 0:
                if scaled_frame.shape[0] != available_h or scaled_frame.shape[1] != available_w:
                    scaled_frame = cv2.resize(scaled_frame, (available_w, available_h), interpolation=cv2.INTER_LINEAR)
                
                result_frame[start_h:end_h, start_w:end_w] = scaled_frame
            
            return result_frame
        
        return clip.transform(scale_transform)
    
    def _ease_out_back(self, t):
        """缓出回弹动画函数（入场时使用）"""
        c1 = 1.70158
        c3 = c1 + 1
        return 1 + c3 * pow(t - 1, 3) + c1 * pow(t - 1, 2)

    def _ease_in_back(self, t):
        """缓入回弹动画函数（出场时使用）"""
        c1 = 1.70158
        c3 = c1 + 1
        return c3 * t * t * t - c1 * t * t

class AvatarLayer:
    """头像层处理器"""
    
    def __init__(self):
        """初始化头像层处理器"""
        # 默认尺寸配置（相对于画布的比例）
        self.size_ratios = {
            AvatarSize.SMALL: 1/6,
            AvatarSize.MEDIUM: 1/4,
            AvatarSize.LARGE: 1/3,
            AvatarSize.HALF: 1/2
        }
        
        # 默认边距（像素）
        self.default_margin = 50
        
        # 初始化动画库
        self.animation_library = AvatarAnimationLibrary()
        
        print("✅ 头像层处理器初始化完成")
    
    def create_avatar_clip(
        self,
        avatar_url: str,
        canvas_size: Tuple[int, int],
        duration: float,
        position: AvatarPosition = AvatarPosition.BOTTOM_RIGHT,
        size: AvatarSize = AvatarSize.MEDIUM,
        style: Optional[AvatarStyle] = None,
        animation: Optional[AvatarAnimation] = None,
        resource_manager = None,
        subtitle_area_height: Optional[int] = None
    ) -> Optional[VideoClip]:
        """
        创建头像视频剪辑
        
        Args:
            avatar_url: 头像视频URL
            canvas_size: 画布尺寸 (width, height)
            duration: 持续时间
            position: 头像位置
            size: 头像尺寸
            style: 样式配置
            animation: 动画配置
            resource_manager: 资源管理器
            
        Returns:
            头像视频剪辑
        """
        try:
            # 获取头像视频文件
            if resource_manager:
                avatar_path = resource_manager.get_resource(avatar_url, "video")
                if not avatar_path:
                    print(f"⚠️  头像视频获取失败: {avatar_url}")
                    return None
            else:
                avatar_path = avatar_url
            
            # 加载头像视频
            avatar_clip = VideoFileClip(str(avatar_path))

            # 获取原始纵横比
            original_aspect_ratio = avatar_clip.w / avatar_clip.h if avatar_clip.h > 0 else None

            # 去除绿幕背景
            avatar_clip = self._remove_green_screen(avatar_clip)

            # 调整时长
            if avatar_clip.duration > duration:
                avatar_clip = avatar_clip.subclipped(0, duration)
            elif avatar_clip.duration < duration:
                # 如果头像视频较短，循环播放
                loops_needed = int(np.ceil(duration / avatar_clip.duration))
                avatar_clip = avatar_clip.with_duration(duration)

            # 计算头像尺寸和位置，保持纵横比，支持字幕对齐
            avatar_size, avatar_position = self._calculate_avatar_layout(
                canvas_size, position, size, original_aspect_ratio, subtitle_area_height
            )
            
            # 调整头像尺寸，确保尺寸有效
            if avatar_size[0] > 0 and avatar_size[1] > 0:
                avatar_clip = avatar_clip.resized(avatar_size)
            else:
                print(f"⚠️  头像尺寸无效: {avatar_size}，使用默认尺寸")
                default_size = (int(canvas_size[0] * 0.25), int(canvas_size[1] * 0.25))
                avatar_clip = avatar_clip.resized(default_size)
                avatar_size = default_size
            
            # 🎯 新方案：双层合成 - 背景层 + Avatar层
            if style and style.is_circular and style.background_color:
                # 创建圆形背景层
                background_layer = self._create_shape_layer(
                    shape_type=ShapeType.CIRCLE,
                    size=avatar_size,
                    duration=duration,
                    color=style.background_color
                )
                
                # 简化Avatar层处理（只需要圆形遮罩，不需要背景色）
                avatar_layer = self._apply_circular_mask_only(avatar_clip, style)
                
                # 对两个层应用相同的动画
                if animation:
                    background_layer = self._apply_avatar_animation(background_layer, animation)
                    avatar_layer = self._apply_avatar_animation(avatar_layer, animation)
                
                # 设置位置
                background_layer = background_layer.with_position(avatar_position)
                avatar_layer = avatar_layer.with_position(avatar_position)
                
                # 合成：背景层在下，avatar层在上
                final_clip = CompositeVideoClip([background_layer, avatar_layer])
                
                print(f"✅ 圆形头像剪辑创建成功(双层合成): {position.value}, 尺寸: {avatar_size}")
                return final_clip
            
            else:
                # 非圆形或无背景色的常规处理
                if style and not style.is_circular:
                    avatar_clip = self._apply_avatar_style(avatar_clip, style)
                elif style and style.is_circular:
                    avatar_clip = self._apply_circular_style(avatar_clip, style)
                
                # 设置位置
                avatar_clip = avatar_clip.with_position(avatar_position)
                
                # 应用动画效果
                if animation:
                    avatar_clip = self._apply_avatar_animation(avatar_clip, animation)
                
                print(f"✅ 头像剪辑创建成功: {position.value}, 尺寸: {avatar_size}")
                return avatar_clip
            
        except Exception as e:
            print(f"❌ 创建头像剪辑失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _calculate_avatar_layout(
        self,
        canvas_size: Tuple[int, int],
        position: AvatarPosition,
        size: AvatarSize,
        original_aspect_ratio: Optional[float] = None,
        subtitle_area_height: Optional[int] = None
    ) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """
        计算头像尺寸和位置，保持原始纵横比，支持字幕对齐

        Args:
            canvas_size: 画布尺寸 (width, height)
            position: 头像位置 (仅支持bottom_left/bottom_right)
            size: 头像尺寸
            original_aspect_ratio: 原始视频纵横比 (width/height)
            subtitle_area_height: 字幕区域高度，用于对齐

        Returns:
            (头像尺寸, 头像位置)
        """
        canvas_width, canvas_height = canvas_size

        # 计算头像尺寸，保持纵横比
        size_ratio = self.size_ratios[size]

        if original_aspect_ratio:
            # 以高度为基准计算，保持纵横比
            avatar_height = int(canvas_height * size_ratio)
            avatar_width = int(avatar_height * original_aspect_ratio)

            # 如果宽度超出限制，以宽度为基准重新计算
            max_width = int(canvas_width * size_ratio)
            if avatar_width > max_width:
                avatar_width = max_width
                avatar_height = int(avatar_width / original_aspect_ratio)
        else:
            # 默认方形
            avatar_width = int(canvas_width * size_ratio)
            avatar_height = int(canvas_height * size_ratio)

        # 计算字幕对齐的Y位置
        subtitle_adjusted_margin = self.default_margin
        if subtitle_area_height:
            # 字幕区域在底部，头像需要在字幕上方，留出一定间距
            subtitle_adjusted_margin = subtitle_area_height + self.default_margin
        
        # 计算位置 - 仅支持底部左右两个位置
        if position == AvatarPosition.BOTTOM_LEFT:
            x = self.default_margin
            y = max(0, canvas_height - avatar_height - subtitle_adjusted_margin)
        elif position == AvatarPosition.BOTTOM_RIGHT:
            x = max(0, canvas_width - avatar_width - self.default_margin)
            y = max(0, canvas_height - avatar_height - subtitle_adjusted_margin)
        else:
            # 不支持的位置，默认使用右下角
            print(f"⚠️  不支持的头像位置: {position}，使用默认位置 bottom_right")
            x = max(0, canvas_width - avatar_width - self.default_margin)
            y = max(0, canvas_height - avatar_height - subtitle_adjusted_margin)

        # 确保位置不会超出画布
        x = min(x, canvas_width - avatar_width) if avatar_width > 0 else 0
        y = min(y, canvas_height - avatar_height) if avatar_height > 0 else 0

        return (avatar_width, avatar_height), (x, y)
    
    def _apply_avatar_style(self, avatar_clip: VideoClip, style: AvatarStyle) -> VideoClip:
        """应用头像样式效果（非圆形样式）"""
        try:
            # 透明度调整
            if style.opacity < 1.0:
                avatar_clip = avatar_clip.with_opacity(style.opacity)

            # 非圆形样式使用常规处理
            if (style.corner_radius > 0 or style.border_width > 0 or
                style.shadow_blur > 0 or style.background_color):
                avatar_clip = self._apply_advanced_style(avatar_clip, style)

            return avatar_clip

        except Exception as e:
            print(f"⚠️  应用头像样式失败: {e}")
            return avatar_clip

    def _apply_circular_style(self, avatar_clip: VideoClip, style: AvatarStyle) -> VideoClip:
        """应用圆形头像样式 - 正确处理背景色和遮罩组合"""
        try:
            # 获取视频尺寸
            w, h = avatar_clip.size
            
            # 第一步：应用透明度调整
            if style.opacity < 1.0:
                avatar_clip = avatar_clip.with_opacity(style.opacity)
            
            # 第二步：如果有背景色，先应用背景色处理
            if style.background_color:
                avatar_clip = self._apply_background_to_circular_avatar(avatar_clip, style.background_color)
            
            # 第三步：创建圆形遮罩并与现有遮罩结合
            def create_circular_mask(get_frame, t):
                # 创建圆形遮罩
                center_x, center_y = w // 2, h // 2
                circle_radius = min(w, h) // 2
                
                # 创建圆形遮罩
                mask = np.zeros((h, w), dtype=np.float32)
                
                # 创建网格
                y, x = np.ogrid[:h, :w]
                
                # 计算到圆心的距离
                distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                
                # 圆形内为1，圆形外为0
                mask[distance <= circle_radius] = 1.0
                
                return mask
            
            # 创建圆形遮罩剪辑
            circular_mask_clip = avatar_clip.transform(create_circular_mask)
            
            # 第四步：与原始遮罩（绿幕去除后的遮罩）结合
            original_mask = avatar_clip.mask
            if original_mask is not None:
                def combine_masks(get_frame, t):
                    try:
                        original_mask_frame = original_mask.get_frame(t)
                        circular_mask_frame = circular_mask_clip.get_frame(t)
                        
                        # 确保数据类型一致
                        if original_mask_frame.dtype != np.float32:
                            original_mask_frame = original_mask_frame.astype(np.float32)
                        if circular_mask_frame.dtype != np.float32:
                            circular_mask_frame = circular_mask_frame.astype(np.float32)
                        
                        # 归一化到0-1范围
                        if original_mask_frame.max() > 1.0:
                            original_mask_frame = original_mask_frame / 255.0
                        if circular_mask_frame.max() > 1.0:
                            circular_mask_frame = circular_mask_frame / 255.0
                        
                        # 组合遮罩：取两个遮罩的交集 (element-wise multiplication)
                        # 只有同时满足：1）不是绿幕区域 2）在圆形内部 的像素才会显示
                        combined_mask = original_mask_frame * circular_mask_frame
                        
                        return combined_mask
                    except Exception as e:
                        print(f"⚠️  遮罩组合失败: {e}")
                        # 发生错误时返回圆形遮罩
                        return circular_mask_clip.get_frame(t)
                
                final_mask = avatar_clip.transform(combine_masks)
            else:
                # 如果没有原始遮罩，直接使用圆形遮罩
                final_mask = circular_mask_clip
            
            # 第五步：应用最终遮罩
            avatar_clip = avatar_clip.with_mask(final_mask)
            
            return avatar_clip
            
        except Exception as e:
            print(f"⚠️  圆形样式应用失败: {e}")
            import traceback
            traceback.print_exc()
            return avatar_clip

    def _apply_circular_mask_only(self, avatar_clip: VideoClip, style: AvatarStyle) -> VideoClip:
        """仅应用圆形遮罩，不处理背景色（用于双层合成方案）"""
        try:
            # 获取视频尺寸
            w, h = avatar_clip.size
            
            # 应用透明度调整
            if style.opacity < 1.0:
                avatar_clip = avatar_clip.with_opacity(style.opacity)
            
            # 创建圆形遮罩
            def create_circular_mask(get_frame, t):
                center_x, center_y = w // 2, h // 2
                circle_radius = min(w, h) // 2
                
                mask = np.zeros((h, w), dtype=np.float32)
                y, x = np.ogrid[:h, :w]
                distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                mask[distance <= circle_radius] = 1.0
                
                return mask
            
            # 创建圆形遮罩剪辑
            circular_mask_clip = avatar_clip.transform(create_circular_mask)
            
            # 与原始遮罩（绿幕去除后的遮罩）结合
            original_mask = avatar_clip.mask
            if original_mask is not None:
                def combine_masks(get_frame, t):
                    try:
                        original_mask_frame = original_mask.get_frame(t)
                        circular_mask_frame = circular_mask_clip.get_frame(t)
                        
                        # 确保数据类型一致
                        if original_mask_frame.dtype != np.float32:
                            original_mask_frame = original_mask_frame.astype(np.float32)
                        if circular_mask_frame.dtype != np.float32:
                            circular_mask_frame = circular_mask_frame.astype(np.float32)
                        
                        # 归一化到0-1范围
                        if original_mask_frame.max() > 1.0:
                            original_mask_frame = original_mask_frame / 255.0
                        if circular_mask_frame.max() > 1.0:
                            circular_mask_frame = circular_mask_frame / 255.0
                        
                        # 组合遮罩：取两个遮罩的交集
                        combined_mask = original_mask_frame * circular_mask_frame
                        
                        return combined_mask
                    except Exception as e:
                        print(f"⚠️  遮罩组合失败: {e}")
                        return circular_mask_clip.get_frame(t)
                
                final_mask = avatar_clip.transform(combine_masks)
            else:
                final_mask = circular_mask_clip
            
            # 应用最终遮罩
            return avatar_clip.with_mask(final_mask)
            
        except Exception as e:
            print(f"⚠️  圆形遮罩应用失败: {e}")
            import traceback
            traceback.print_exc()
            return avatar_clip

    def _apply_advanced_style(self, avatar_clip: VideoClip, style: AvatarStyle) -> VideoClip:
        """应用高级样式效果（圆角、边框、阴影等）- 不处理圆形头像"""
        def style_transform(get_frame, t):
            try:
                frame = get_frame(t)

                # 检查输入帧的形状和数据类型
                if len(frame.shape) != 3 or frame.shape[2] != 3:
                    print(f"⚠️  输入帧形状不正确: {frame.shape}, 期望: (h, w, 3)")
                    return frame

                # 确保输入帧是正确的数据类型
                if frame.dtype != np.uint8:
                    frame = (frame * 255).astype(np.uint8) if frame.max() <= 1.0 else frame.astype(np.uint8)

                # 转换为OpenCV格式
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                
                # 非圆形的常规处理
                frame_rgba = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2RGBA)
                frame_rgba = frame_rgba.astype(np.uint8)

                # 应用圆角
                if style.corner_radius > 0:
                    frame_rgba = self._apply_rounded_corners(frame_rgba, style.corner_radius, False)

                # 应用背景色
                if style.background_color:
                    frame_rgba = self._apply_background_color(frame_rgba, style.background_color)

                # 应用边框
                if style.border_width > 0:
                    frame_rgba = self._apply_border(frame_rgba, style.border_width, style.border_color)

                # 转换回RGB
                frame_rgb = cv2.cvtColor(frame_rgba[:,:,:3], cv2.COLOR_BGR2RGB)

                # 确保输出帧的数据类型正确
                if frame_rgb.dtype != np.uint8:
                    frame_rgb = frame_rgb.astype(np.uint8)

                return frame_rgb

            except Exception as e:
                print(f"⚠️  样式变换失败: {e}")
                import traceback
                traceback.print_exc()
                return frame

        return avatar_clip.transform(style_transform)



    def _apply_background_color(self, frame_rgba: np.ndarray, background_color: str) -> np.ndarray:
        """应用背景色，填充透明区域"""
        try:
            # 解析背景色
            bg_color_rgb = self._hex_to_rgb(background_color)
            h, w = frame_rgba.shape[:2]

            # 确保frame_rgba是正确的数据类型和形状
            if len(frame_rgba.shape) != 3 or frame_rgba.shape[2] != 4:
                print(f"⚠️  frame_rgba形状不正确: {frame_rgba.shape}, 期望: (h, w, 4)")
                return frame_rgba

            if frame_rgba.dtype != np.uint8:
                frame_rgba = frame_rgba.astype(np.uint8)

            # 创建背景色图像 (BGR格式)
            background_bgr = np.full((h, w, 3), (bg_color_rgb[2], bg_color_rgb[1], bg_color_rgb[0]), dtype=np.uint8)
            
            # 获取alpha通道
            alpha = frame_rgba[:,:,3]
            
            # 归一化alpha通道为0-1范围
            alpha_normalized = alpha.astype(np.float32) / 255.0
            
            # 创建alpha遮罩 (3通道)
            alpha_mask = np.stack([alpha_normalized, alpha_normalized, alpha_normalized], axis=2)
            
            # 混合前景和背景
            foreground_bgr = frame_rgba[:,:,:3]
            blended_bgr = (foreground_bgr.astype(np.float32) * alpha_mask + 
                          background_bgr.astype(np.float32) * (1 - alpha_mask)).astype(np.uint8)
            
            # 更新frame_rgba的BGR通道
            frame_rgba[:,:,:3] = blended_bgr
            
            # 将alpha通道设置为255（完全不透明）
            frame_rgba[:,:,3] = 255

            return frame_rgba

        except Exception as e:
            print(f"⚠️  背景色应用失败: {e}, frame_rgba.shape={frame_rgba.shape}, frame_rgba.dtype={frame_rgba.dtype}")
            return frame_rgba

    def _apply_rounded_corners(self, frame_rgba: np.ndarray, radius: int, is_circular: bool = False) -> np.ndarray:
        """应用圆角或圆形效果"""
        try:
            h, w = frame_rgba.shape[:2]

            # 确保frame_rgba是正确的数据类型和形状
            if len(frame_rgba.shape) != 3 or frame_rgba.shape[2] != 4:
                print(f"⚠️  frame_rgba形状不正确: {frame_rgba.shape}, 期望: (h, w, 4)")
                return frame_rgba

            if frame_rgba.dtype != np.uint8:
                frame_rgba = frame_rgba.astype(np.uint8)

            if is_circular:
                # 圆形头像：以较小的边为直径
                center_x, center_y = w // 2, h // 2
                circle_radius = min(w, h) // 2

                # 创建圆形遮罩
                mask = np.zeros((h, w), dtype=np.uint8)
                cv2.circle(mask, (center_x, center_y), circle_radius, 255, -1)
            else:
                # 圆角矩形
                mask = np.zeros((h, w), dtype=np.uint8)
                cv2.rectangle(mask, (radius, 0), (w-radius, h), 255, -1)
                cv2.rectangle(mask, (0, radius), (w, h-radius), 255, -1)

                # 四个角的圆形
                cv2.circle(mask, (radius, radius), radius, 255, -1)
                cv2.circle(mask, (w-radius, radius), radius, 255, -1)
                cv2.circle(mask, (radius, h-radius), radius, 255, -1)
                cv2.circle(mask, (w-radius, h-radius), radius, 255, -1)

            # 应用遮罩 - 确保数据类型和形状匹配
            alpha_channel = frame_rgba[:,:,3]
            if alpha_channel.shape != mask.shape:
                print(f"⚠️  alpha通道形状不匹配: alpha={alpha_channel.shape}, mask={mask.shape}")
                return frame_rgba

            # 确保数据类型匹配
            alpha_channel = alpha_channel.astype(np.uint8)
            mask_result = cv2.bitwise_and(alpha_channel, mask)
            frame_rgba[:,:,3] = mask_result

            return frame_rgba

        except Exception as e:
            print(f"⚠️  圆角处理失败: {e}, frame_rgba.shape={frame_rgba.shape}, frame_rgba.dtype={frame_rgba.dtype}")
            return frame_rgba

    def _apply_border(self, frame_rgba: np.ndarray, width: int, color: str) -> np.ndarray:
        """应用边框效果"""
        try:
            # 解析颜色
            color_rgb = self._hex_to_rgb(color)
            h, w = frame_rgba.shape[:2]

            # 确保frame_rgba是正确的数据类型
            if frame_rgba.dtype != np.uint8:
                frame_rgba = frame_rgba.astype(np.uint8)

            # 绘制边框 - 注意OpenCV使用BGR格式，所以需要反转RGB
            # 确保颜色值是整数且在有效范围内
            color_bgra = (
                int(color_rgb[2]) & 0xFF,  # B
                int(color_rgb[1]) & 0xFF,  # G
                int(color_rgb[0]) & 0xFF,  # R
                255                        # A
            )

            # 绘制边框
            cv2.rectangle(frame_rgba, (0, 0), (w-1, h-1), color_bgra, width)

            return frame_rgba

        except Exception as e:
            print(f"⚠️  边框绘制失败: {e}, frame_rgba.shape={frame_rgba.shape}, frame_rgba.dtype={frame_rgba.dtype}")
            return frame_rgba

    def _hex_to_rgb(self, hex_color: str) -> Tuple[int, int, int]:
        """将十六进制颜色转换为RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def _apply_avatar_animation(self, avatar_clip: VideoClip, animation: AvatarAnimation) -> VideoClip:
        """应用头像动画效果"""
        try:
            effects = []

            # 使用缩放动画替代淡入淡出效果
            if animation.fade_in_duration > 0 or animation.fade_out_duration > 0:
                avatar_clip = self._apply_scale_animation(
                    avatar_clip,
                    fade_in_duration=animation.fade_in_duration if animation.fade_in_duration > 0 else 0.0,
                    fade_out_duration=animation.fade_out_duration if animation.fade_out_duration > 0 else 0.0
                )

            # 入场动画
            if animation.entrance_effect:
                avatar_clip = self._apply_entrance_effect(avatar_clip, animation.entrance_effect)

            # 退场动画
            if animation.exit_effect:
                avatar_clip = self._apply_exit_effect(avatar_clip, animation.exit_effect)

            # 应用所有效果
            if effects:
                avatar_clip = avatar_clip.with_effects(effects)

            return avatar_clip

        except Exception as e:
            print(f"⚠️  应用头像动画失败: {e}")
            return avatar_clip

    def _apply_entrance_effect(self, avatar_clip: VideoClip, effect: str) -> VideoClip:
        """应用入场动画效果"""
        if effect == "slide_in":
            # 滑入效果（从右侧滑入）
            def slide_in_transform(get_frame, t):
                frame = get_frame(t)
                if t < 0.5:  # 前0.5秒执行滑入
                    progress = t / 0.5
                    offset_x = int((1 - progress) * frame.shape[1])
                    # 创建偏移后的帧
                    new_frame = np.zeros_like(frame)
                    if offset_x < frame.shape[1]:
                        new_frame[:, :-offset_x] = frame[:, offset_x:]
                    return new_frame
                return frame
            return avatar_clip.transform(slide_in_transform)

        elif effect == "zoom_in":
            # 缩放入场效果
            def zoom_in_transform(get_frame, t):
                frame = get_frame(t)
                if t < 0.5:  # 前0.5秒执行缩放
                    progress = t / 0.5
                    scale = 0.5 + 0.5 * progress  # 从50%缩放到100%
                    h, w = frame.shape[:2]
                    new_h, new_w = int(h * scale), int(w * scale)
                    resized = cv2.resize(frame, (new_w, new_h))

                    # 居中放置
                    new_frame = np.zeros_like(frame)
                    start_y = (h - new_h) // 2
                    start_x = (w - new_w) // 2
                    new_frame[start_y:start_y+new_h, start_x:start_x+new_w] = resized
                    return new_frame
                return frame
            return avatar_clip.transform(zoom_in_transform)

        return avatar_clip

    def _apply_exit_effect(self, avatar_clip: VideoClip, effect: str) -> VideoClip:
        """应用退场动画效果"""
        # 类似入场效果，但在视频结尾执行
        return avatar_clip

    def _remove_green_screen(self, avatar_clip: VideoClip) -> VideoClip:
        """去除绿幕背景，使用优化的自适应算法"""
        try:
            def adaptive_green_screen_mask(get_frame, t):
                """创建自适应绿幕遮罩"""
                frame = get_frame(t)

                # 转换为HSV色彩空间
                hsv = cv2.cvtColor(frame, cv2.COLOR_RGB2HSV)

                # 获取自适应阈值
                lower_green, upper_green = self._get_adaptive_green_thresholds(hsv)

                # 创建绿色遮罩
                mask = cv2.inRange(hsv, lower_green, upper_green)

                # 应用高级边缘处理
                refined_mask = self._apply_advanced_edge_processing(mask)

                # 反转遮罩（保留非绿色部分）
                final_mask = 1.0 - refined_mask

                return final_mask.astype(np.float32)

            # 应用绿幕遮罩
            mask_clip = avatar_clip.transform(adaptive_green_screen_mask)
            avatar_clip = avatar_clip.with_mask(mask_clip)

            print(f"✅ 优化绿幕去除完成")
            return avatar_clip

        except Exception as e:
            print(f"⚠️  绿幕去除失败，使用原始视频: {e}")
            return avatar_clip

    def _get_adaptive_green_thresholds(self, hsv_frame: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """获取自适应绿幕阈值"""
        try:
            # 预处理绿幕的基础参数
            base_hue_range = (45, 75)  # 绿色色相范围
            min_saturation = 40
            min_value = 40
            
            # 初步检测绿色区域
            initial_lower = np.array([base_hue_range[0], min_saturation, min_value])
            initial_upper = np.array([base_hue_range[1], 255, 255])
            
            initial_mask = cv2.inRange(hsv_frame, initial_lower, initial_upper)
            green_pixels = hsv_frame[initial_mask > 0]
            
            # 如果检测到足够的绿色像素，进行自适应调整
            if len(green_pixels) > 100:
                # 计算实际绿色的统计特征
                hue_mean = float(np.mean(green_pixels[:, 0]))
                sat_mean = float(np.mean(green_pixels[:, 1]))
                val_mean = float(np.mean(green_pixels[:, 2]))
                
                # 基于实际分布调整阈值
                hue_tolerance = 8   # 色相容忍度
                sat_tolerance = 30  # 饱和度容忍度  
                val_tolerance = 30  # 明度容忍度
                
                adaptive_lower = np.array([
                    max(base_hue_range[0], hue_mean - hue_tolerance),
                    max(min_saturation, sat_mean - sat_tolerance),
                    max(min_value, val_mean - val_tolerance)
                ], dtype=np.uint8)
                
                adaptive_upper = np.array([
                    min(base_hue_range[1], hue_mean + hue_tolerance),
                    255,
                    255
                ], dtype=np.uint8)
                
                return adaptive_lower, adaptive_upper
            
            # 使用默认阈值
            return initial_lower, initial_upper
            
        except Exception as e:
            print(f"⚠️  自适应阈值计算失败，使用默认值: {e}")
            # 返回原始固定阈值作为后备
            return np.array([35, 40, 40]), np.array([85, 255, 255])

    def _apply_advanced_edge_processing(self, mask: np.ndarray) -> np.ndarray:
        """应用高级边缘处理"""
        try:
            # 1. 形态学优化 - 去除细小噪点
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            
            # 2. 距离变换羽化
            dist_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
            feather_radius = 6  # 羽化半径
            feather_mask = np.clip(dist_transform / feather_radius, 0, 1)
            
            # 3. 双重高斯模糊 - 创建更自然的边缘
            feather_mask = cv2.GaussianBlur(feather_mask, (5, 5), 1.0)
            feather_mask = cv2.GaussianBlur(feather_mask, (3, 3), 0.5)
            
            return feather_mask.astype(np.float32)
            
        except Exception as e:
            print(f"⚠️  高级边缘处理失败，使用基础处理: {e}")
            # 后备方案：使用原始的简单处理
            kernel = np.ones((3, 3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            mask = cv2.GaussianBlur(mask, (5, 5), 0)
            return mask.astype(np.float32) / 255.0

    def parse_avatar_config(self, avatar_config: Dict[str, Any]) -> Tuple[AvatarPosition, AvatarSize, AvatarStyle, AvatarAnimation]:
        """
        解析头像配置字典

        Args:
            avatar_config: 头像配置字典

        Returns:
            (位置, 尺寸, 样式, 动画) 配置元组
        """
        # 解析位置
        position_str = avatar_config.get("position", "bottom_left")  # 修改默认值为bottom_left
        try:
            position = AvatarPosition(position_str)
        except ValueError:
            position = AvatarPosition.BOTTOM_LEFT  # 修改默认值为bottom_left

        # 解析尺寸
        size_str = avatar_config.get("size", "medium")
        try:
            size = AvatarSize(size_str)
        except ValueError:
            size = AvatarSize.MEDIUM

        # 解析样式
        style_config = avatar_config.get("style", {})
        style = AvatarStyle(
            border_width=style_config.get("border_width", 0),
            border_color=style_config.get("border_color", "#FFFFFF"),
            corner_radius=style_config.get("corner_radius", 0),
            is_circular=style_config.get("is_circular", False),
            shadow_blur=style_config.get("shadow_blur", 0),
            shadow_offset=tuple(style_config.get("shadow_offset", [0, 0])),
            shadow_color=style_config.get("shadow_color", "#000000"),
            background_color=style_config.get("background_color"),
            opacity=style_config.get("opacity", 1.0)
        )

        # 解析动画
        animation_config = avatar_config.get("animation", {})
        animation = AvatarAnimation(
            fade_in_duration=animation_config.get("fade_in_duration", 0.5),
            fade_out_duration=animation_config.get("fade_out_duration", 0.3),
            entrance_effect=animation_config.get("entrance_effect"),
            exit_effect=animation_config.get("exit_effect"),
            enable_fade_in=animation_config.get("enable_fade_in"),
            enable_fade_out=animation_config.get("enable_fade_out")
        )

        return position, size, style, animation

    def create_smart_avatar_clips(
        self,
        scenes_with_avatar: List[Tuple[int, Any, float, float]],  # (scene_index, scene, start_time, duration)
        canvas_size: Tuple[int, int],
        resource_manager = None
    ) -> List[VideoClip]:
        """
        创建智能淡入淡出的头像剪辑序列

        Args:
            scenes_with_avatar: 包含头像的场景列表 [(场景索引, 场景对象, 开始时间, 持续时间)]
            canvas_size: 画布尺寸
            resource_manager: 资源管理器

        Returns:
            头像剪辑列表
        """
        if not scenes_with_avatar:
            return []

        avatar_clips = []
        fade_duration = 0.5  # 默认淡入淡出时长

        for i, (scene_index, scene, start_time, duration) in enumerate(scenes_with_avatar):
            try:
                # 解析头像配置
                position, size, style, animation = self.parse_avatar_config({
                    "position": getattr(scene.avatar, 'position', None),
                    "size": getattr(scene.avatar, 'size', None),
                    "style": getattr(scene.avatar, 'style', None) or {},
                    "animation": getattr(scene.avatar, 'animation', None) or {}
                })

                # 创建基础头像剪辑
                avatar_clip = self.create_avatar_clip(
                    avatar_url=scene.avatar.url,
                    canvas_size=canvas_size,
                    duration=duration,
                    position=position,
                    size=size,
                    style=style,
                    animation=None,  # 不使用原有动画，使用智能动画
                    resource_manager=resource_manager
                )

                if not avatar_clip:
                    continue

                # 判断是否需要淡入淡出 - 支持配置文件明确指定
                # animation配置已经在前面解析过了，直接使用

                # 淡入逻辑：配置优先，然后自动判断
                if animation.enable_fade_in is not None:
                    # 配置文件明确指定
                    need_fade_in = animation.enable_fade_in
                    print(f"    🎛️  配置指定淡入: {need_fade_in}")
                else:
                    # 自动判断
                    need_fade_in = False
                    if i == 0:
                        # 第一个头像，需要淡入
                        need_fade_in = True
                    else:
                        # 检查与前一个头像是否连续
                        prev_scene_index = scenes_with_avatar[i-1][0]
                        if scene_index > prev_scene_index + 1:
                            # 不连续，需要淡入
                            need_fade_in = True
                    print(f"    🤖 自动判断淡入: {need_fade_in}")

                # 淡出逻辑：配置优先，然后自动判断
                if animation.enable_fade_out is not None:
                    # 配置文件明确指定
                    need_fade_out = animation.enable_fade_out
                    print(f"    🎛️  配置指定淡出: {need_fade_out}")
                else:
                    # 自动判断
                    need_fade_out = False
                    if i == len(scenes_with_avatar) - 1:
                        # 最后一个头像，需要淡出
                        need_fade_out = True
                    else:
                        # 检查与下一个头像是否连续
                        next_scene_index = scenes_with_avatar[i+1][0]
                        if next_scene_index > scene_index + 1:
                            # 不连续，需要淡出
                            need_fade_out = True
                    print(f"    🤖 自动判断淡出: {need_fade_out}")

                # 使用缩放动画替代淡入淡出效果
                if need_fade_in or need_fade_out:
                    avatar_clip = self._apply_scale_animation(
                        avatar_clip,
                        fade_in_duration=fade_duration if need_fade_in else 0.0,
                        fade_out_duration=fade_duration if need_fade_out else 0.0
                    )

                    if need_fade_in:
                        print(f"    🎭 头像淡入: 场景{scene_index + 1}")
                    if need_fade_out:
                        print(f"    🎭 头像淡出: 场景{scene_index + 1}")

                # 设置时间偏移
                avatar_clip = avatar_clip.with_start(start_time)
                avatar_clips.append(avatar_clip)

                print(f"    ✅ 智能头像剪辑: 场景{scene_index + 1}, 淡入={need_fade_in}, 淡出={need_fade_out}")

            except Exception as e:
                print(f"    ❌ 创建头像剪辑失败: 场景{scene_index + 1}, {e}")
                continue

        return avatar_clips

    def _apply_scale_animation(self, clip: VideoClip, fade_in_duration: float = 0.0, fade_out_duration: float = 0.0) -> VideoClip:
        """
        使用新的动画库系统应用缩放动画效果
        
        参数:
        - fade_in_duration: 缩放入场时长（从80%到100%大小）
        - fade_out_duration: 缩放出场时长（从100%到0%大小）
        """
        if fade_in_duration <= 0 and fade_out_duration <= 0:
            return clip
        
        try:
            # 使用新的动画库系统
            result_clip = clip
            
            # 应用入场动画
            if fade_in_duration > 0:
                in_config = AnimationConfig(
                    type=AnimationType.SCALE_IN,
                    duration=fade_in_duration,
                    easing_function="ease_out_back",
                    intensity=1.0
                )
                result_clip = self.animation_library.apply_animation(result_clip, in_config)
            
            # 应用退场动画
            if fade_out_duration > 0:
                out_config = AnimationConfig(
                    type=AnimationType.SCALE_OUT,
                    duration=fade_out_duration,
                    easing_function="ease_in_back",
                    intensity=1.0
                )
                result_clip = self.animation_library.apply_animation(result_clip, out_config)
            
            return result_clip
            
        except Exception as e:
            print(f"⚠️  动画库应用失败，使用备用方案: {e}")
            # 备用方案：使用原有的实现
            return self._apply_legacy_scale_animation(clip, fade_in_duration, fade_out_duration)

    def _apply_legacy_scale_animation(self, clip: VideoClip, fade_in_duration: float = 0.0, fade_out_duration: float = 0.0) -> VideoClip:
        """
        原有的缩放动画实现（备用方案）
        
        参数:
        - fade_in_duration: 缩放入场时长（从80%到100%大小）
        - fade_out_duration: 缩放出场时长（从100%到0%大小）
        """
        try:
            duration = clip.duration

            def scale_function(t):
                """计算时间t处的缩放比例"""
                scale = 1.0

                # 缩放入场效果：从80%放大到100%
                if fade_in_duration > 0 and t < fade_in_duration:
                    progress = t / fade_in_duration
                    # 使用缓动函数，让动画更自然
                    eased_progress = self._ease_out_back(progress)
                    scale = 0.8 + (1.0 - 0.8) * eased_progress

                # 缩放出场效果：从100%缩小到0%
                elif fade_out_duration > 0 and t > (duration - fade_out_duration):
                    time_from_start = t - (duration - fade_out_duration)
                    progress = time_from_start / fade_out_duration
                    # 使用缓动函数，让动画更自然
                    eased_progress = self._ease_in_back(progress)
                    scale = 1.0 - 1.0 * eased_progress

                return max(0.01, min(2.0, scale))  # 限制缩放范围，最小0.01避免完全消失

            # 使用MoviePy的resize方法，并设置居中
            return clip.resized(scale_function).with_position('center')

        except Exception as e:
            print(f"⚠️  备用缩放动画失败: {e}")
            return clip

    def _ease_out_back(self, t):
        """
        缓出回弹动画函数（入场时使用）
        创造轻微的"弹跳"效果，让头像入场更有活力
        """
        c1 = 1.70158
        c3 = c1 + 1
        return 1 + c3 * pow(t - 1, 3) + c1 * pow(t - 1, 2)

    def _ease_in_back(self, t):
        """
        缓入回弹动画函数（出场时使用）
        创造轻微的"回拉"效果，让头像出场更自然
        """
        c1 = 1.70158
        c3 = c1 + 1
        return c3 * t * t * t - c1 * t * t

    def _apply_opacity_fade_effects(self, clip: VideoClip, fade_in_duration: float = 0.0, fade_out_duration: float = 0.0, fade_type: str = "custom"):
        """
        应用透明度淡入淡出效果（避免变黑问题）

        参数:
        - fade_in_duration: 淡入时长
        - fade_out_duration: 淡出时长
        - fade_type: 效果类型
          - "none": 无效果（直接显示/隐藏）
          - "custom": 使用自定义透明度动画（推荐，避免形状不匹配）⭐
          - "cross": 使用CrossFadeIn/CrossFadeOut（可能有形状问题）
          - "simple": 使用FadeIn/FadeOut（可能有形状问题）
        """
        if fade_in_duration <= 0 and fade_out_duration <= 0:
            return clip

        if fade_type == "none":
            # 无渐变效果，直接显示/隐藏
            return clip

        elif fade_type == "custom":
            # 使用自定义透明度动画（避免形状不匹配问题）
            return self._apply_custom_opacity_animation(clip, fade_in_duration, fade_out_duration)

        elif fade_type == "cross":
            # 使用CrossFadeIn/CrossFadeOut（可能有形状问题）
            try:
                effects_to_apply = []
                if fade_in_duration > 0:
                    effects_to_apply.append(vfx.CrossFadeIn(fade_in_duration))
                if fade_out_duration > 0:
                    effects_to_apply.append(vfx.CrossFadeOut(fade_out_duration))

                if effects_to_apply:
                    return clip.with_effects(effects_to_apply)
                else:
                    return clip
            except Exception as e:
                print(f"⚠️  CrossFade效果失败，回退到自定义动画: {e}")
                return self._apply_custom_opacity_animation(clip, fade_in_duration, fade_out_duration)

        elif fade_type == "simple":
            # 使用标准FadeIn/FadeOut（可能有形状问题）
            try:
                effects_to_apply = []
                if fade_in_duration > 0:
                    effects_to_apply.append(vfx.FadeIn(fade_in_duration))
                if fade_out_duration > 0:
                    effects_to_apply.append(vfx.FadeOut(fade_out_duration))

                if effects_to_apply:
                    return clip.with_effects(effects_to_apply)
                else:
                    return clip
            except Exception as e:
                print(f"⚠️  Fade效果失败，回退到自定义动画: {e}")
                return self._apply_custom_opacity_animation(clip, fade_in_duration, fade_out_duration)

        else:
            # 默认使用自定义效果
            return self._apply_custom_opacity_animation(clip, fade_in_duration, fade_out_duration)

    def _apply_custom_opacity_animation(self, clip: VideoClip, fade_in_duration: float, fade_out_duration: float) -> VideoClip:
        """
        应用自定义透明度动画（避免形状不匹配问题）
        """
        try:
            duration = clip.duration

            # 创建透明度函数
            def opacity_function(t):
                """计算时间t处的透明度"""
                opacity = 1.0

                # 淡入效果
                if fade_in_duration > 0 and t < fade_in_duration:
                    opacity = min(opacity, t / fade_in_duration)

                # 淡出效果
                if fade_out_duration > 0 and t > (duration - fade_out_duration):
                    fade_progress = (duration - t) / fade_out_duration
                    opacity = min(opacity, fade_progress)

                return max(0.0, min(1.0, opacity))

            # 使用MoviePy的with_opacity方法
            return clip.with_opacity(opacity_function)

        except Exception as e:
            print(f"⚠️  自定义透明度动画失败: {e}")
            # 如果自定义动画失败，尝试简单的分段处理
            return self._apply_simple_fade_segments(clip, fade_in_duration, fade_out_duration)

    def _apply_simple_fade_segments(self, clip: VideoClip, fade_in_duration: float, fade_out_duration: float) -> VideoClip:
        """
        使用分段方式应用淡入淡出（备用方案）
        """
        try:
            duration = clip.duration
            segments = []

            # 淡入段
            if fade_in_duration > 0:
                fade_in_clip = clip.subclipped(0, fade_in_duration)
                # 使用固定透明度值的渐变
                for i in range(int(fade_in_duration * 10)):  # 每0.1秒一个段
                    t_start = i * 0.1
                    t_end = min((i + 1) * 0.1, fade_in_duration)
                    if t_end > t_start:
                        opacity = t_end / fade_in_duration
                        segment = fade_in_clip.subclipped(t_start, t_end).with_opacity(opacity)
                        segments.append(segment)

            # 中间段（完全不透明）
            middle_start = fade_in_duration if fade_in_duration > 0 else 0
            middle_end = duration - (fade_out_duration if fade_out_duration > 0 else 0)

            if middle_end > middle_start:
                middle_clip = clip.subclipped(middle_start, middle_end)
                segments.append(middle_clip)

            # 淡出段
            if fade_out_duration > 0:
                fade_out_start = duration - fade_out_duration
                fade_out_clip = clip.subclipped(fade_out_start, duration)
                # 使用固定透明度值的渐变
                for i in range(int(fade_out_duration * 10)):  # 每0.1秒一个段
                    t_start = i * 0.1
                    t_end = min((i + 1) * 0.1, fade_out_duration)
                    if t_end > t_start:
                        opacity = (fade_out_duration - t_end) / fade_out_duration
                        segment = fade_out_clip.subclipped(t_start, t_end).with_opacity(max(0.0, opacity))
                        segments.append(segment)

            # 合并所有段
            if segments:
                from moviepy.editor import concatenate_videoclips
                return concatenate_videoclips(segments)
            else:
                return clip

        except Exception as e:
            print(f"⚠️  分段淡入淡出失败: {e}")
            return clip

    def _apply_background_to_circular_avatar(self, avatar_clip: VideoClip, background_color: str) -> VideoClip:
        """为圆形头像应用背景色 - 在透明区域填充背景色"""
        try:
            # 解析背景色
            bg_color_rgb = self._hex_to_rgb(background_color)
            w, h = avatar_clip.size
            
            def apply_background(get_frame, t):
                frame = get_frame(t)
                
                # 确保帧数据类型正确
                if frame.dtype != np.uint8:
                    frame = (frame * 255).astype(np.uint8) if frame.max() <= 1.0 else frame.astype(np.uint8)
                
                # 获取绿幕去除后的遮罩
                original_mask = avatar_clip.mask
                if original_mask is not None:
                    mask_frame = original_mask.get_frame(t)
                    
                    # 归一化遮罩
                    if mask_frame.dtype != np.float32:
                        mask_frame = mask_frame.astype(np.float32)
                    if mask_frame.max() > 1.0:
                        mask_frame = mask_frame / 255.0
                    
                    # 创建背景
                    background = np.full((h, w, 3), bg_color_rgb, dtype=np.uint8)
                    
                    # 扩展遮罩到3通道
                    if len(mask_frame.shape) == 2:
                        mask_3d = np.stack([mask_frame, mask_frame, mask_frame], axis=2)
                    else:
                        mask_3d = mask_frame
                    
                    # 混合：有内容的地方显示头像，透明的地方显示背景色
                    result = (frame.astype(np.float32) * mask_3d + 
                             background.astype(np.float32) * (1 - mask_3d)).astype(np.uint8)
                    
                    return result
                else:
                    # 如果没有遮罩，直接返回原帧
                    return frame
            
            # 创建处理后的视频剪辑
            processed_clip = avatar_clip.transform(apply_background)
            
            # 保持原始遮罩
            if avatar_clip.mask is not None:
                processed_clip = processed_clip.with_mask(avatar_clip.mask)
            
            return processed_clip
            
        except Exception as e:
            print(f"⚠️  背景色应用失败: {e}")
            return avatar_clip

    def _create_shape_layer(
        self,
        shape_type: ShapeType,
        size: Tuple[int, int],
        duration: float,
        color: str = "#FFFFFF",
        corner_radius: int = 0
    ) -> VideoClip:
        """
        创建形状背景层
        
        Args:
            shape_type: 形状类型
            size: 尺寸 (width, height)
            duration: 持续时间
            color: 颜色 (hex格式)
            corner_radius: 圆角半径（仅对圆角矩形有效）
            
        Returns:
            形状背景层VideoClip
        """
        try:
            # 解析颜色
            color_rgb = self._hex_to_rgb(color)
            w, h = size
            
            # 创建纯色背景
            background_clip = ColorClip(size=size, color=color_rgb, duration=duration)
            
            # 根据形状类型创建遮罩
            if shape_type == ShapeType.CIRCLE:
                # 圆形遮罩
                def create_circle_mask(get_frame, t):
                    center_x, center_y = w // 2, h // 2
                    circle_radius = min(w, h) // 2
                    
                    mask = np.zeros((h, w), dtype=np.float32)
                    y, x = np.ogrid[:h, :w]
                    distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                    mask[distance <= circle_radius] = 1.0
                    
                    return mask
                
                mask_clip = background_clip.transform(create_circle_mask)
                
            elif shape_type == ShapeType.RECTANGLE:
                # 矩形遮罩（无需处理，默认就是矩形）
                return background_clip
                
            elif shape_type == ShapeType.ROUNDED_RECTANGLE:
                # 圆角矩形遮罩
                def create_rounded_mask(get_frame, t):
                    mask = np.zeros((h, w), dtype=np.uint8)
                    
                    # 使用cv2创建圆角矩形遮罩
                    if corner_radius > 0:
                        # 中心矩形
                        cv2.rectangle(mask, (corner_radius, 0), (w-corner_radius, h), 255, -1)
                        cv2.rectangle(mask, (0, corner_radius), (w, h-corner_radius), 255, -1)
                        
                        # 四个角的圆形
                        cv2.circle(mask, (corner_radius, corner_radius), corner_radius, 255, -1)
                        cv2.circle(mask, (w-corner_radius, corner_radius), corner_radius, 255, -1)
                        cv2.circle(mask, (corner_radius, h-corner_radius), corner_radius, 255, -1)
                        cv2.circle(mask, (w-corner_radius, h-corner_radius), corner_radius, 255, -1)
                    else:
                        # 如果半径为0，就是普通矩形
                        mask.fill(255)
                    
                    return mask.astype(np.float32) / 255.0
                
                mask_clip = background_clip.transform(create_rounded_mask)
                
            else:
                # 默认矩形
                return background_clip
            
            # 应用遮罩
            return background_clip.with_mask(mask_clip)
            
        except Exception as e:
            print(f"⚠️  形状层创建失败: {e}")
            import traceback
            traceback.print_exc()
            # 返回默认的纯色背景
            return ColorClip(size=size, color=self._hex_to_rgb(color), duration=duration)

# 全局头像层处理器实例
avatar_layer = AvatarLayer()
