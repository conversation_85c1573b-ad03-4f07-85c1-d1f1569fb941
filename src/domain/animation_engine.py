#!/usr/bin/env python3
"""
🎬 Reavo Video Creator - 动画引擎
Animation Engine - 统一的动画处理系统

集成所有动画功能：基础动画、房地产专业动画、缓动函数等
"""

import json
import numpy as np
from pathlib import Path
from typing import Dict, Any, Callable, List, Tuple
from moviepy import VideoClip
from dataclasses import dataclass
from enum import Enum

class EasingType(Enum):
    """缓动函数类型"""
    LINEAR = "linear"
    EASE_IN_OUT = "ease_in_out"
    EASE_IN = "ease_in"
    EASE_OUT = "ease_out"

@dataclass
class AnimationConfig:
    """动画配置"""
    type: str
    intensity: float = 1.0
    duration: float = None
    easing: EasingType = EasingType.EASE_IN_OUT

class AnimationEngine:
    """动画引擎 - 统一处理所有动画效果"""
    
    def __init__(self):
        """初始化动画引擎"""
        self.real_estate_presets = self._load_real_estate_presets()
        self.current_theme = "luxury_home"
        self.animation_index = 0
        
        # 动画强度设置
        self.base_zoom_factor = 0.05  # 基础缩放因子 5%
        self.intensity_multiplier = 2.5  # 强度倍数
        
        print("✅ 动画引擎已加载")
    
    def _load_real_estate_presets(self) -> Dict[str, Any]:
        """加载房地产动画预设"""
        try:
            preset_path = Path("configs/real_estate_animation_presets.json")
            if preset_path.exists():
                with open(preset_path, 'r', encoding='utf-8') as f:
                    presets = json.load(f)
                print(f"✅ 加载房地产动画预设: {len(presets)} 个主题")
                return presets
        except Exception as e:
            print(f"⚠️  加载动画预设失败: {e}")
        
        # 默认预设
        return {
            "luxury_home": {
                "zoom_in": {"intensity": 1.0, "easing": "ease_in_out"},
                "zoom_out": {"intensity": 1.0, "easing": "ease_in_out"}
            }
        }
    
    def apply_background_animation(self, background_clip: VideoClip, animation_config: Dict[str, Any]) -> VideoClip:
        """应用背景动画效果"""
        try:
            animation_type = animation_config.get("type", "auto")
            intensity = animation_config.get("intensity", 1.0)
            duration = background_clip.duration
            
            # 自动选择动画类型
            if animation_type == "auto":
                animation_type = self._get_auto_animation_type()
            
            # 应用缩放动画
            if animation_type in ["zoom_in", "zoom_out"]:
                return self._apply_zoom_animation(background_clip, animation_type, intensity, duration)
            
            print(f"⚠️  未知动画类型: {animation_type}")
            return background_clip
            
        except Exception as e:
            print(f"⚠️  动画应用失败: {e}")
            return background_clip
    
    def _get_auto_animation_type(self) -> str:
        """自动选择动画类型（交替）"""
        animation_types = ["zoom_in", "zoom_out"]
        selected = animation_types[self.animation_index % len(animation_types)]
        self.animation_index += 1
        return selected
    
    def _apply_zoom_animation(self, background_clip: VideoClip, animation_type: str, intensity: float, duration: float) -> VideoClip:
        """应用缩放动画"""
        # 计算缩放范围
        zoom_factor = self.base_zoom_factor * self.intensity_multiplier * intensity
        
        if animation_type == "zoom_in":
            zoom_start, zoom_end = 1.0, 1.0 + zoom_factor
            print(f"   📈 放大动画: {zoom_start:.3f} → {zoom_end:.3f}")
        else:  # zoom_out
            zoom_start, zoom_end = 1.0 + zoom_factor, 1.0
            print(f"   📉 缩小动画: {zoom_start:.3f} → {zoom_end:.3f}")
        
        return self._create_zoom_effect(background_clip, zoom_start, zoom_end, duration)
    
    def _create_zoom_effect(self, background_clip: VideoClip, zoom_start: float, zoom_end: float, duration: float) -> VideoClip:
        """创建缩放效果，确保无黑边"""
        target_width, target_height = 1920, 1080
        
        def make_frame(get_frame, t):
            # 计算当前缩放比例
            progress = t / duration
            eased_progress = progress * progress * (3.0 - 2.0 * progress)  # ease_in_out
            current_zoom = zoom_start + (zoom_end - zoom_start) * eased_progress
            
            # 获取原始帧
            frame = get_frame(t)
            
            # 使用OpenCV进行缩放和裁剪
            import cv2
            
            original_h, original_w = frame.shape[:2]
            scaled_w = int(original_w * current_zoom)
            scaled_h = int(original_h * current_zoom)
            
            # 缩放图像
            scaled_frame = cv2.resize(frame, (scaled_w, scaled_h), interpolation=cv2.INTER_LINEAR)
            
            # 居中裁剪到目标尺寸
            if scaled_w >= target_width and scaled_h >= target_height:
                start_y = (scaled_h - target_height) // 2
                start_x = (scaled_w - target_width) // 2
                result_frame = scaled_frame[start_y:start_y+target_height, start_x:start_x+target_width]
            else:
                # 如果缩放后小于目标尺寸，创建黑色背景并居中放置
                result_frame = np.zeros((target_height, target_width, 3), dtype=np.uint8)
                start_y = (target_height - scaled_h) // 2
                start_x = (target_width - scaled_w) // 2
                result_frame[start_y:start_y+scaled_h, start_x:start_x+scaled_w] = scaled_frame
            
            return result_frame
        
        return background_clip.transform(make_frame)
    
    def get_easing_function(self, easing_type: EasingType) -> Callable[[float], float]:
        """获取缓动函数"""
        if easing_type == EasingType.LINEAR:
            return lambda t: t
        elif easing_type == EasingType.EASE_IN:
            return lambda t: t * t
        elif easing_type == EasingType.EASE_OUT:
            return lambda t: 1 - (1 - t) * (1 - t)
        else:  # EASE_IN_OUT
            return lambda t: t * t * (3.0 - 2.0 * t)

# 全局动画引擎实例
animation_engine = AnimationEngine()
