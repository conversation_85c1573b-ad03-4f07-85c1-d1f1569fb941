#!/usr/bin/env python3
"""
Reavo Video Creator - 视频生成器
整合所有组件生成最终视频
"""

from moviepy import (
    VideoFileClip, VideoClip,
    CompositeVideoClip, concatenate_videoclips
)
from pathlib import Path
from typing import List, Optional, Dict, Any
import time

from application.config import StandardConfig, ConfigProcessor
from domain.renderer import TemplateRenderer
from application.resources import ResourceManager

class VideoGenerator:
    """视频生成器 - 整合所有组件生成最终视频"""
    
    def __init__(self):
        """初始化视频生成器"""
        self.resource_manager = ResourceManager()
        self.config_processor = ConfigProcessor()
        self.template_renderer = TemplateRenderer(self.resource_manager)
        
        print("🎬 视频生成器初始化完成")
    
    def generate_video(self, config_path: Path, output_filename: Optional[str] = None) -> Path:
        """
        生成视频
        
        Args:
            config_path: 配置文件路径
            output_filename: 输出文件名（可选）
            
        Returns:
            输出视频文件路径
        """
        print(f"🚀 开始生成视频: {config_path}")
        start_time = time.time()
        
        try:
            # 1. 加载和处理配置
            config = self.config_processor.load_config(config_path)
            print(f"📋 配置名称: {config.basic.output.filename}")
            
            # 2. 获取配置摘要
            summary = self.config_processor.get_config_summary(config)
            print(f"📊 配置摘要: {summary}")
            
            # 3. 渲染视频片段
            video_clips = self._render_video_clips(config)
            
            if not video_clips:
                raise Exception("无法渲染任何视频片段")
            
            # 4. 组合视频片段
            final_video = self._combine_video_clips(video_clips, config)
            
            # 5. 添加音频轨道
            final_video = self._add_audio_track(final_video, config)
            
            # 6. 导出视频
            output_path = self._export_video(final_video, config, output_filename)
            
            # 7. 清理资源
            self._cleanup_resources(final_video)
            
            elapsed_time = time.time() - start_time
            print(f"✅ 视频生成完成！")
            print(f"   - 输出文件: {output_path}")
            print(f"   - 耗时: {elapsed_time:.2f}秒")
            
            return output_path
            
        except Exception as e:
            print(f"❌ 视频生成失败: {e}")
            raise
    
    def _render_video_clips(self, config: StandardConfig) -> List[VideoClip]:
        """渲染所有视频片段"""
        print("🎨 渲染视频片段...")
        
        clips = []
        
        # 渲染片头
        if config.intro:
            intro_clip = self.template_renderer.render_intro(config)
            if intro_clip:
                clips.append(intro_clip)
        
        # 渲染内容片段
        for segment in config.segments:
            segment_clip = self.template_renderer.render_content_segment(segment, config)
            if segment_clip:
                clips.append(segment_clip)
        
        # 渲染片尾
        if config.outro:
            outro_clip = self.template_renderer.render_outro(config)
            if outro_clip:
                clips.append(outro_clip)
        
        print(f"✅ 共渲染 {len(clips)} 个视频片段")
        return clips
    
    def _combine_video_clips(self, clips: List[VideoClip], config: StandardConfig) -> VideoClip:
        """组合视频片段"""
        print("🔗 组合视频片段...")
        
        if len(clips) == 1:
            return clips[0]
        
        # 使用 concatenate_videoclips 拼接片段，优化内存使用
        final_clip = concatenate_videoclips(clips, method="compose")
        
        # 获取质量预设中的fps
        quality_preset = self.config_processor.get_quality_preset(config.basic.output.quality)
        target_fps = quality_preset.get('fps', 30)
        
        # 设置视频属性 - 仅在需要时设置
        if final_clip.fps != target_fps:
            final_clip = final_clip.with_fps(target_fps)
        
        total_duration = sum(clip.duration for clip in clips)
        print(f"✅ 视频组合完成，总时长: {total_duration:.2f}秒")
        
        return final_clip
    
    def _add_audio_track(self, video_clip: VideoClip, config: StandardConfig) -> VideoClip:
        """添加音频轨道"""
        print("🎵 添加音频轨道...")
        
        # 渲染音频轨道
        audio_track = self.template_renderer.render_audio_track(config)
        
        if audio_track:
            # 确保音频长度与视频匹配 - 优化音频处理
            video_duration = video_clip.duration
            
            if abs(audio_track.duration - video_duration) > 0.1:  # 只在差异较大时调整
                if audio_track.duration > video_duration:
                    audio_track = audio_track.subclipped(0, video_duration)
                else:
                    # 如果音频较短，使用静音填充而不是循环
                    audio_track = audio_track.with_duration(video_duration)
            
            # 将音频添加到视频
            video_clip = video_clip.with_audio(audio_track)
            print("✅ 音频轨道添加完成")
        else:
            print("⚠️  未找到音频轨道")
        
        return video_clip
    
    def _export_video(self, video_clip: VideoClip, config: StandardConfig, output_filename: Optional[str] = None) -> Path:
        """导出视频"""
        print("💾 导出视频...")
        
        # 生成输出文件名
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"{config.basic.output.filename}_{timestamp}.mp4"
        
        if not output_filename.endswith('.mp4'):
            output_filename += '.mp4'
        
        output_path = self.resource_manager.get_output_path(output_filename)
        
        # 获取质量预设
        quality_preset = self.config_processor.get_quality_preset(config.basic.output.quality)
        
        # 优化的导出参数 - 根据质量配置调整
        export_params = {
            'codec': 'libx264',
            'audio_codec': 'aac',
            'bitrate': quality_preset['bitrate'],
            'audio_bitrate': quality_preset.get('audio_bitrate', '192k'),
            'fps': quality_preset.get('fps', 30),
            'preset': 'medium',          # 使用中等预设平衡质量和速度
            'threads': 4,                # 使用多线程
            'temp_audiofile': 'temp_audio.m4a',  # 使用临时音频文件
            'remove_temp': True          # 自动清理临时文件
        }
        
        print(f"🎯 使用{config.basic.output.quality}质量设置 (码率: {quality_preset['bitrate']})")
        
        # 导出视频
        try:
            video_clip.write_videofile(str(output_path), **export_params)
            print(f"✅ 视频导出完成: {output_path}")
            return output_path
        except Exception as e:
            print(f"❌ 视频导出失败: {e}")
            raise
    
    def _cleanup_resources(self, video_clip: VideoClip):
        """清理资源"""
        print("🧹 清理资源...")
        
        try:
            # 关闭视频剪辑
            video_clip.close()
            
            # 清理临时文件
            self.resource_manager.cleanup_temp_files()
            
            print("✅ 资源清理完成")
        except Exception as e:
            print(f"⚠️  资源清理失败: {e}")
    
    def get_video_info(self, config_path: Path) -> Dict[str, Any]:
        """
        获取视频信息（不生成视频）
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            视频信息字典
        """
        config = self.config_processor.load_config(config_path)
        total_duration = self.template_renderer.get_total_duration(config)
        
        return {
            "title": config.basic.output.filename,
            "quality": config.basic.output.quality,
            "template": "default",  # 在V3配置中暂时使用默认值
            "resolution": config.basic.output.resolution,
            "fps": self.config_processor.get_quality_preset(config.basic.output.quality).get('fps', 30),
            "total_duration": total_duration,
            "segments_count": len(config.segments),
            "has_intro": config.intro is not None,
            "has_outro": config.outro is not None,
            "has_background_music": config.basic.audio.background.get('music') is not None if config.basic.audio.background else False,
            "total_narrations": sum(len(seg.scenes) for seg in config.segments)
        }
    
    def validate_config(self, config_path: Path) -> Dict[str, Any]:
        """
        验证配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            验证结果
        """
        try:
            config = self.config_processor.load_config(config_path)
            
            warnings = []
            errors = []
            
            # 检查基本配置
            if not config.basic.output.filename:
                errors.append("视频文件名不能为空")
            
            if len(config.segments) == 0:
                errors.append("至少需要一个视频片段")
            
            # 检查资源可用性
            for segment in config.segments:
                for scene in segment.scenes:
                    audio_url = scene.audio.url
                    if audio_url and not self.resource_manager.get_resource(audio_url, "audio"):
                        warnings.append(f"音频资源无法访问: {audio_url}")
            
            if config.basic.audio.background and config.basic.audio.background.get("music"):
                if not self.resource_manager.get_resource(config.basic.audio.background["music"], "audio"):
                    warnings.append(f"背景音乐无法访问: {config.basic.audio.background['music']}")
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "config_summary": self.config_processor.get_config_summary(config)
            }
            
        except Exception as e:
            return {
                "valid": False,
                "errors": [f"配置文件解析失败: {str(e)}"],
                "warnings": [],
                "config_summary": None
            }
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return self.resource_manager.get_cache_info()
    
    def clear_cache(self):
        """清理缓存"""
        self.resource_manager.clear_cache() 