#!/usr/bin/env python3
"""
Reavo Video Creator - 资源管理器
负责处理远程文件下载、缓存管理和本地文件处理
"""

import os
import hashlib
import requests
from pathlib import Path
from typing import Optional, Union
from urllib.parse import urlparse
import tempfile
import shutil

class ResourceManager:
    """资源管理器 - 处理所有文件资源的下载和管理"""
    
    def __init__(self, cache_dir: str = "cache", temp_dir: str = "temp"):
        """
        初始化资源管理器
        
        Args:
            cache_dir: 缓存目录
            temp_dir: 临时文件目录
        """
        # 指向项目根目录
        self.base_dir = Path(__file__).parent.parent.parent
        self.cache_dir = self.base_dir / cache_dir
        self.temp_dir = self.base_dir / temp_dir
        self.output_dir = self.base_dir / "output"
        
        # 创建必要的目录
        self.cache_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"🗂️  资源管理器初始化完成")
        print(f"   - 缓存目录: {self.cache_dir}")
        print(f"   - 临时目录: {self.temp_dir}")
        print(f"   - 输出目录: {self.output_dir}")
    
    def get_resource(self, url_or_path: str, resource_type: str = "media") -> Optional[Path]:
        """
        获取资源文件路径（自动处理URL下载或本地文件）
        
        Args:
            url_or_path: URL或本地文件路径
            resource_type: 资源类型（用于缓存分类）
            
        Returns:
            本地文件路径，如果失败返回None
        """
        if not url_or_path:
            return None
        
        # 检查是否为URL
        if url_or_path.startswith(('http://', 'https://')):
            return self._download_file(url_or_path, resource_type)
        else:
            # 本地文件路径处理
            local_path = Path(url_or_path)
            if local_path.is_absolute():
                return local_path if local_path.exists() else None
            else:
                # 相对路径，相对于项目根目录
                abs_path = self.base_dir / local_path
                return abs_path if abs_path.exists() else None
    
    def _download_file(self, url: str, resource_type: str) -> Optional[Path]:
        """
        下载远程文件并缓存
        
        Args:
            url: 文件URL
            resource_type: 资源类型
            
        Returns:
            本地缓存文件路径
        """
        try:
            # 生成缓存文件名
            url_hash = hashlib.md5(url.encode()).hexdigest()
            parsed_url = urlparse(url)
            file_extension = Path(parsed_url.path).suffix or '.tmp'
            cache_filename = f"{resource_type}_{url_hash}{file_extension}"
            cache_path = self.cache_dir / cache_filename
            
            # 检查缓存
            if cache_path.exists():
                print(f"✅ 使用缓存文件: {cache_filename}")
                return cache_path
            
            # 下载文件
            print(f"🌐 下载文件: {url}")
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            # 保存到缓存
            with open(cache_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            print(f"✅ 文件下载完成: {cache_filename}")
            return cache_path
            
        except Exception as e:
            print(f"❌ 文件下载失败: {url} - {e}")
            return None
    
    def create_temp_file(self, suffix: str = ".tmp") -> Path:
        """
        创建临时文件
        
        Args:
            suffix: 文件后缀
            
        Returns:
            临时文件路径
        """
        temp_file = tempfile.NamedTemporaryFile(
            suffix=suffix, 
            dir=self.temp_dir, 
            delete=False
        )
        temp_file.close()
        return Path(temp_file.name)
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            for temp_file in self.temp_dir.glob("*"):
                if temp_file.is_file():
                    temp_file.unlink()
            print("🧹 临时文件清理完成")
        except Exception as e:
            print(f"⚠️  临时文件清理失败: {e}")
    
    def get_output_path(self, filename: str) -> Path:
        """
        获取输出文件路径
        
        Args:
            filename: 输出文件名
            
        Returns:
            完整的输出路径
        """
        return self.output_dir / filename
    
    def clear_cache(self):
        """清理缓存"""
        try:
            for cache_file in self.cache_dir.glob("*"):
                if cache_file.is_file():
                    cache_file.unlink()
            print("🗑️  缓存清理完成")
        except Exception as e:
            print(f"⚠️  缓存清理失败: {e}")
    
    def get_cache_info(self) -> dict:
        """获取缓存信息"""
        cache_files = list(self.cache_dir.glob("*"))
        total_size = sum(f.stat().st_size for f in cache_files if f.is_file())
        
        return {
            "cache_dir": str(self.cache_dir),
            "file_count": len(cache_files),
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "files": [f.name for f in cache_files if f.is_file()]
        }
    
    def __del__(self):
        """析构函数 - 清理临时文件"""
        self.cleanup_temp_files() 