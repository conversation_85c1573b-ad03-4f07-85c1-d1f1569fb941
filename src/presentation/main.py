#!/usr/bin/env python3
"""
Reavo Video Creator - 主程序入口
智能视频制作工具 MVP 版本
"""

import argparse
import sys
from pathlib import Path
from typing import Optional
import json
from moviepy import (
    VideoFileClip, AudioFileClip,
    TextClip, ColorClip, ImageClip,
    CompositeVideoClip, CompositeAudioClip,
    concatenate_videoclips
)
from application.video_generator import VideoGenerator

def print_banner():
    """打印程序横幅"""
    print("=" * 60)
    print("🎬 Reavo Video Creator - MVP 版本")
    print("   智能视频制作工具 - 通过配置文件驱动视频生成")
    print("=" * 60)

def print_quality_info():
    """打印质量预设的说明"""
    print("\n📋 支持的视频质量预设:")
    print("\n🎯 推荐质量预设:")
    print("   📱 standard - 标准质量 (720p, 适合快速分享)")
    print("   🖥️  high     - 高清质量 (1080p, 平衡画质与体积)")
    print("   🎬 ultra    - 超高清质量 (4K, 专业制作)")
    
    print("\n🔧 兼容性预设:")
    print("   📹 720p/1080p/4k - 兼容旧版本配置")
    
    print("\n💡 所有用户都使用统一的V3四层配置架构")
    print("   - basic: 基础设置 (输出、音频、质量)")
    print("   - intro: 开场片段 (标题、背景、动画)")
    print("   - segments: 主要内容 (场景、音频、字幕、特效)")
    print("   - outro: 结束片段 (感谢、联系信息)")
    
    print("\n🎨 通过调整配置复杂度和质量预设来满足不同需求")

def validate_config_file(config_path: Path) -> bool:
    """验证配置文件路径"""
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    if not config_path.is_file():
        print(f"❌ 路径不是文件: {config_path}")
        return False
    
    if config_path.suffix.lower() != '.json':
        print(f"❌ 配置文件必须是JSON格式: {config_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            json.load(f)
        return True
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 文件读取错误: {e}")
        return False

def generate_video(config_path: Path, output_filename: Optional[str] = None) -> bool:
    """生成视频"""
    try:
        generator = VideoGenerator()
        output_path = generator.generate_video(config_path, output_filename)
        
        print(f"\n🎉 视频生成成功!")
        print(f"📁 输出文件: {output_path}")
        
        return True
    except Exception as e:
        print(f"\n❌ 视频生成失败: {e}")
        return False

def show_video_info(config_path: Path) -> bool:
    """显示视频信息"""
    try:
        generator = VideoGenerator()
        info = generator.get_video_info(config_path)
        
        print(f"\n📊 视频信息:")
        print(f"   标题: {info['title']}")
        print(f"   质量: {info['quality']}")
        print(f"   模板: {info['template']}")
        print(f"   分辨率: {info['resolution'][0]}x{info['resolution'][1]}")
        print(f"   帧率: {info['fps']} FPS")
        print(f"   总时长: {info['total_duration']:.2f}秒")
        print(f"   片段数: {info['segments_count']}")
        print(f"   场景数: {info['total_narrations']}")
        print(f"   包含片头: {'是' if info['has_intro'] else '否'}")
        print(f"   包含片尾: {'是' if info['has_outro'] else '否'}")
        print(f"   背景音乐: {'是' if info['has_background_music'] else '否'}")
        
        return True
    except Exception as e:
        print(f"\n❌ 获取视频信息失败: {e}")
        return False

def validate_config(config_path: Path) -> bool:
    """验证配置文件"""
    try:
        generator = VideoGenerator()
        result = generator.validate_config(config_path)
        
        print(f"\n✅ 配置验证完成")
        print(f"   有效性: {'✅ 有效' if result['valid'] else '❌ 无效'}")
        
        if result['errors']:
            print(f"\n❌ 错误:")
            for error in result['errors']:
                print(f"   - {error}")
        
        if result['warnings']:
            print(f"\n⚠️  警告:")
            for warning in result['warnings']:
                print(f"   - {warning}")
        
        if result['config_summary']:
            summary = result['config_summary']
            print(f"\n📋 配置摘要:")
            print(f"   文件名: {summary['filename']}")
            print(f"   质量: {summary['quality']}")
            print(f"   分辨率: {summary['resolution'][0]}x{summary['resolution'][1]}")
            print(f"   片段数: {summary['segments_count']}")
            print(f"   场景数: {summary['total_scenes']}")
        
        return result['valid']
    except Exception as e:
        print(f"\n❌ 配置验证失败: {e}")
        return False

def show_cache_info():
    """显示缓存信息"""
    try:
        generator = VideoGenerator()
        cache_info = generator.get_cache_info()
        
        print(f"\n📦 缓存信息:")
        print(f"   缓存目录: {cache_info['cache_dir']}")
        print(f"   文件数量: {cache_info['file_count']}")
        print(f"   总大小: {cache_info['total_size_mb']} MB")
        
        if cache_info['files']:
            print(f"\n📂 缓存文件:")
            for file_name in cache_info['files']:
                print(f"   - {file_name}")
        
        return True
    except Exception as e:
        print(f"\n❌ 获取缓存信息失败: {e}")
        return False

def clear_cache():
    """清理缓存"""
    try:
        generator = VideoGenerator()
        generator.clear_cache()
        print(f"\n✅ 缓存清理完成")
        return True
    except Exception as e:
        print(f"\n❌ 缓存清理失败: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    parser = argparse.ArgumentParser(
        description="Reavo Video Creator - 智能视频制作工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py generate protocol.json                  # 生成完整视频
  python main.py generate protocol.json -o my_video.mp4  # 指定输出文件名
  python main.py info protocol.json                      # 查看视频信息
  python main.py validate protocol.json                  # 验证配置文件
  python main.py cache --info                            # 查看缓存信息
  python main.py cache --clear                           # 清理缓存
  python main.py --quality                               # 查看质量预设说明
        """
    )
    
    # 添加方案说明选项
    parser.add_argument(
        '--quality',
        action='store_true',
        help='显示支持的视频质量预设说明'
    )
    
    # 创建子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # generate 命令
    generate_parser = subparsers.add_parser('generate', help='生成完整视频')
    generate_parser.add_argument('config', type=Path, help='配置文件路径')
    generate_parser.add_argument('-o', '--output', type=str, help='输出文件名')
    
    # info 命令
    info_parser = subparsers.add_parser('info', help='显示视频信息')
    info_parser.add_argument('config', type=Path, help='配置文件路径')
    
    # validate 命令
    validate_parser = subparsers.add_parser('validate', help='验证配置文件')
    validate_parser.add_argument('config', type=Path, help='配置文件路径')
    
    # cache 命令
    cache_parser = subparsers.add_parser('cache', help='缓存管理')
    cache_group = cache_parser.add_mutually_exclusive_group(required=True)
    cache_group.add_argument('--info', action='store_true', help='显示缓存信息')
    cache_group.add_argument('--clear', action='store_true', help='清理缓存')
    
    # 解析参数
    args = parser.parse_args()
    
    # 处理质量预设说明
    if args.quality:
        print_quality_info()
        return 0
    
    # 检查是否提供了命令
    if not args.command:
        parser.print_help()
        return 1
    
    # 处理各种命令
    try:
        if args.command == 'generate':
            if not validate_config_file(args.config):
                return 1
            success = generate_video(args.config, args.output)
            return 0 if success else 1
        
        elif args.command == 'info':
            if not validate_config_file(args.config):
                return 1
            success = show_video_info(args.config)
            return 0 if success else 1
        
        elif args.command == 'validate':
            if not validate_config_file(args.config):
                return 1
            success = validate_config(args.config)
            return 0 if success else 1
        
        elif args.command == 'cache':
            if args.info:
                success = show_cache_info()
                return 0 if success else 1
            elif args.clear:
                success = clear_cache()
                return 0 if success else 1
        
        else:
            parser.print_help()
            return 1
    
    except KeyboardInterrupt:
        print(f"\n\n⚠️  用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 程序执行错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 