alwaysApply: true

# 📚 Reavo Video Creator 文档体系规范

## 🎯 规范目的

本规范确保AI助手和开发团队正确使用、维护和贡献项目文档，保持文档体系的一致性和高质量。

## 📁 文档结构标准

### 核心原则
- **单一职责**: 每个分类只有一个主文档
- **内容完整**: 主文档包含该分类的全部信息
- **结构清晰**: 统一的格式和组织方式
- **易于维护**: 减少重复，便于更新

### 标准化文档结构
```
docs/
├── README.md                           # 📚 文档中心导航（必需）
├── standards/                          # 🛠️ 开发规范目录
│   └── development-guide.md           # 🛠️ 开发规范主文档（必需）
├── references/                         # 📚 参考文档目录
│   └── MoviePy_API_Document.md        # 🎬 MoviePy API 技术参考（必需）
├── guides/user-guide.md               # 📚 使用指南主文档（必需）
├── development-log/project-log.md     # 📊 开发日志主文档（必需）
└── todos/development-roadmap.md       # 📋 开发路线图主文档（必需）
```

## 📖 各文档职责定义

### 1. docs/README.md - 文档中心导航
**职责**: 文档体系的统一入口和导航
**内容标准**:
- 项目概述和核心特性
- 文档结构说明和快速导航
- 项目状态速览
- 使用建议和角色导航
- 快速开始指南

**维护规则**:
- 项目重大变更时更新项目概述
- 文档结构变化时同步更新导航
- 保持与各主文档的一致性

### 2. standards/development-guide.md - 开发规范主文档
**职责**: 项目开发的统一标准和规范
**内容标准**:
- 架构设计和模块职责
- Python编码标准和类型注解
- MoviePy 2.x 使用规范
- 开发流程和代码审查
- 测试规范和质量保证
- 文档规范和最佳实践

**维护规则**:
- 技术栈升级时更新相关规范
- 发现新的最佳实践时及时补充
- 架构变更时同步更新设计说明
- 所有开发者必须遵循此规范

### 2.1. references/MoviePy_API_Document.md - MoviePy API 技术参考
**职责**: 基于官方文档优化整理的 MoviePy 2.x API 参考手册
**内容标准**:
- 优化整理的 MoviePy 2.x API 参考（基于官方文档）
- 基类和继承关系详解
- 视频、音频剪辑类的完整用法
- 时间操作方法和效果系统
- 版本差异说明和最佳实践
- 故障排除和调试技巧

**维护规则**:
- MoviePy 版本更新时同步更新 API 说明
- 发现新的 API 用法时及时补充
- 所有 MoviePy 相关代码必须遵循此文档规范
- 与 development-guide.md 保持 MoviePy 使用的一致性
- 保持与官方文档的准确性和完整性

**特殊说明**:
- references/ 目录用于存放基于官方文档优化整理的参考资料
- 这些文档保持官方文档的准确性，同时提供更好的阅读体验
- 需要定期跟随官方文档更新而更新

### 3. guides/user-guide.md - 使用指南主文档
**职责**: 用户和开发者的完整使用指南
**内容标准**:
- 快速开始和基本命令
- 配置文件详解（简化配置 + 完整配置）
- 实战案例和样式定制指南
- 高级功能和批量处理
- 常见问题和解决方案

**维护规则**:
- 新功能发布时更新配置说明
- 用户反馈的问题及时添加到FAQ
- 实战案例保持最新和有效
- 确保所有示例代码可执行

### 4. development-log/project-log.md - 开发日志主文档
**职责**: 项目开发历史和状态的完整记录
**内容标准**:
- 开发阶段记录和里程碑
- 技术架构和核心模块分析
- 关键技术决策和实施效果
- 功能特性统计和性能基准
- 测试验证记录和稳定性数据

**维护规则**:
- 重要功能完成后及时更新
- 技术决策必须记录背景和理由
- 性能数据定期更新
- 保持时间线的连续性

### 5. todos/development-roadmap.md - 开发路线图主文档
**职责**: 功能规划和开发计划的系统管理
**内容标准**:
- 短期、中期、长期开发计划
- 优先级分类和时间规划
- 技术债务清单和解决方案
- 里程碑规划和开发建议

**维护规则**:
- 计划变更时及时更新优先级
- 功能完成后移动到开发日志
- 新需求评估后添加到相应优先级
- 定期回顾和调整计划

## 🔧 文档使用规范

### AI助手使用规则

#### 1. 信息查找顺序
```
1. 首先查看 docs/README.md 了解整体结构
2. 根据问题类型选择对应的主文档：
   - 开发问题 → standards/development-guide.md
   - MoviePy API 问题 → references/MoviePy_API_Document.md
   - 使用问题 → guides/user-guide.md
   - 历史信息 → development-log/project-log.md
   - 功能规划 → todos/development-roadmap.md
3. 在主文档内使用目录快速定位具体信息
4. 如需跨文档信息，参考主README的导航
```

#### 2. 文档引用规范
```markdown
# 正确的引用方式
- 查看 [开发规范](docs/standards/development-guide.md) 的架构设计章节
- 参考 [MoviePy API 文档](docs/references/MoviePy_API_Document.md) 的视频效果章节
- 参考 [使用指南](docs/guides/user-guide.md) 的配置详解部分
- 详见 [开发日志](docs/development-log/project-log.md) 的技术决策记录

# 错误的引用方式（已废弃）
- ❌ 查看 docs/standards/development-standards.md
- ❌ 参考 docs/guides/configuration-guide.md
- ❌ 详见 docs/development-log/project-analysis.md
```

#### 3. 信息更新原则
- **单一来源**: 每类信息只在一个主文档中维护
- **同步更新**: 修改信息时同步更新所有相关引用
- **版本一致**: 确保各文档间的项目状态信息一致
- **完整性检查**: 更新后验证文档的完整性和准确性

### 开发者使用规则

#### 1. 新手入门流程
```
1. 阅读 docs/README.md 了解项目和文档结构
2. 学习 docs/standards/development-guide.md 掌握开发规范
3. 参考 docs/guides/user-guide.md 了解功能使用
4. 查看 docs/todos/development-roadmap.md 了解开发计划
```

#### 2. 日常开发参考
```
- 编码规范 → development-guide.md 的编码标准章节
- API使用 → development-guide.md 的MoviePy使用规范
- MoviePy API 详解 → references/MoviePy_API_Document.md 的完整API参考
- 配置格式 → user-guide.md 的配置文件详解
- 架构理解 → project-log.md 的技术架构章节
```

#### 3. 问题解决路径
```
1. 使用问题 → user-guide.md 的常见问题章节
2. 开发问题 → development-guide.md 的相关章节
3. MoviePy API 问题 → references/MoviePy_API_Document.md 的故障排除章节
4. 历史问题 → project-log.md 的技术决策记录
5. 新需求 → development-roadmap.md 查看是否已规划
```

## 📝 文档维护规范

### 内容更新规则

#### 1. 必须更新的情况
- ✅ 新功能发布时更新使用指南
- ✅ 架构变更时更新开发规范
- ✅ MoviePy 版本升级时更新 API 文档
- ✅ 重要决策时更新开发日志
- ✅ 计划调整时更新开发路线图
- ✅ 项目状态变化时更新主README

#### 2. 更新内容要求
- **准确性**: 信息必须准确、最新
- **完整性**: 相关信息要完整覆盖
- **一致性**: 各文档间信息保持一致
- **可操作性**: 示例和指南必须可执行

#### 3. 更新流程
```
1. 确定更新范围和影响的文档
2. 在对应的主文档中进行修改
3. 检查是否需要更新主README的导航
4. 验证修改后的一致性和准确性
5. 提交时说明文档变更内容
```

### 质量保证标准

#### 1. 格式标准
- 使用统一的Markdown格式
- 保持一致的标题层级结构
- 使用标准的emoji图标系统
- 代码块必须指定语言类型

#### 2. 内容标准
- 信息必须准确和最新
- 示例代码必须可执行
- 链接必须有效
- 专业术语使用一致

#### 3. 可读性标准
- 清晰的章节结构
- 适当的内容长度
- 有效的导航和目录
- 用户友好的语言表达

## 🚫 禁止操作

### 严格禁止的行为
- ❌ **创建分散的小文档**: 每个分类只能有一个主文档
- ❌ **重复内容**: 不同文档间不得有重复的实质性内容
- ❌ **破坏结构**: 不得随意添加新的文档分类
- ❌ **创建README**: 除主README外，不得在子目录创建README文件
- ❌ **引用废弃文档**: 不得引用已删除或合并的旧文档

### 需要谨慎的操作
- ⚠️ **结构调整**: 文档结构变更需要同步更新所有相关引用

## 💻 代码开发流程规范

### 🚨 关键原则
**在修改任何现有功能或添加新功能前，必须彻底理解相关的现有代码逻辑**

### 📋 强制开发流程

#### 1. 代码分析阶段（必须完成）
```
✅ 完整阅读相关模块的所有代码
   - 主要类和方法的实现逻辑
   - 数据流和处理流程  
   - 依赖关系和调用链
   - 已有的设计模式

✅ 理解现有架构设计
   - 模块职责和边界
   - 接口设计和数据结构
   - 错误处理机制
   - 性能考虑

✅ 分析问题根因
   - 确定问题的真正原因
   - 理解为什么现有代码产生问题
   - 评估修改的影响范围
   - 考虑向后兼容性
```

#### 2. 设计方案阶段
```
✅ 基于现有架构设计解决方案
   - 在现有框架内寻找解决方案
   - 优先考虑最小化修改
   - 保持代码风格一致性
   - 遵循现有的设计原则

✅ 评估方案可行性
   - 技术可行性分析
   - 性能影响评估
   - 测试复杂度考虑
   - 维护成本估算
```

#### 3. 实施阶段
```
✅ 渐进式修改
   - 小步骤修改，及时测试
   - 保留现有功能的稳定性
   - 详细的代码注释
   - 清晰的提交信息

✅ 充分测试验证
   - 单元测试覆盖
   - 集成测试验证
   - 回归测试确保
   - 性能基准测试
```

### ❌ 禁止的错误行为

#### 1. 盲目修改代码
```
❌ 不理解现有逻辑就开始修改
❌ 基于假设而非事实进行开发
❌ 重写已有功能而非改进
❌ 忽略现有的设计模式
```

#### 2. 缺乏系统性分析
```
❌ 只看部分代码就下结论
❌ 不分析问题的根本原因
❌ 不考虑修改的连锁影响
❌ 不验证解决方案的完整性
```

#### 3. 违背架构原则
```
❌ 破坏现有的模块边界
❌ 引入不一致的设计模式
❌ 增加不必要的复杂性
❌ 忽略性能和维护性考虑
```

### 🎯 最佳实践

#### 1. 代码理解技巧
```
📚 使用工具辅助分析
   - 代码搜索和交叉引用
   - 调用关系图生成
   - 数据流分析
   - 静态代码分析

🔍 深度阅读方法
   - 从入口点开始跟踪
   - 重点关注核心流程
   - 理解错误处理逻辑
   - 分析性能关键路径
```

#### 2. 问题诊断方法
```
🐛 系统性问题定位
   - 重现问题的最小案例
   - 分析日志和错误信息
   - 使用调试工具追踪
   - 对比正常和异常情况

📊 性能分析方法
   - 基准测试建立
   - 瓶颈点识别
   - 资源使用分析
   - 算法复杂度评估
```

#### 3. 修改验证标准
```
✅ 功能正确性验证
   - 原有功能保持不变
   - 新功能按预期工作
   - 边界条件处理正确
   - 错误处理机制有效

✅ 代码质量保证
   - 代码可读性良好
   - 注释清晰完整
   - 命名规范一致
   - 结构逻辑清晰
```

### 📝 典型案例总结

#### 错误案例：圆形头像功能修改
```
❌ 问题分析错误
   - 没有理解现有的绿幕去除机制
   - 不清楚MoviePy的遮罩处理逻辑
   - 基于错误假设设计解决方案

❌ 实施方法错误
   - 在错误的处理阶段进行修改
   - 重写已有功能而非改进
   - 没有测试修改的副作用

❌ 结果验证不足
   - 没有全面测试所有场景
   - 没有对比修改前后的效果
   - 没有验证性能影响
```

#### 正确流程示例
```
✅ 正确的分析流程
   1. 完整理解头像处理的整个pipeline
   2. 深入分析绿幕去除的具体实现
   3. 理解MoviePy遮罩机制的工作原理
   4. 确定圆形处理应该在哪个阶段进行

✅ 正确的实施方法
   1. 在现有框架内设计解决方案
   2. 最小化修改现有代码
   3. 保持与现有代码风格一致
   4. 充分测试和验证修改效果
```

### 🔄 持续改进

#### 1. 学习和反思
```
📈 定期回顾代码修改
   - 分析修改的成功和失败
   - 总结经验教训
   - 改进分析和实施方法
   - 更新最佳实践文档

🎓 技术知识提升
   - 深入学习相关技术栈
   - 理解框架和库的设计原理
   - 关注行业最佳实践
   - 参与代码审查和讨论
```

#### 2. 流程优化
```
🔧 工具和方法改进
   - 引入更好的分析工具
   - 建立更完善的测试环境
   - 改进调试和问题定位方法
   - 优化代码审查流程

📊 质量度量改进
   - 建立代码质量指标
   - 跟踪修改成功率
   - 监控性能影响
   - 收集用户反馈
```
- ⚠️ **删除内容**: 删除内容前确认没有其他地方依赖

## 🔄 持续改进机制

### 定期审查
- **月度审查**: 检查文档的准确性和完整性
- **季度更新**: 根据项目发展调整文档结构
- **版本审查**: 重大版本发布时全面审查文档

### 反馈机制
- 收集用户和开发者的文档使用反馈
- 根据反馈持续优化文档结构和内容
- 定期评估文档的有效性和实用性

### 质量指标
- 文档的使用频率和有效性
- 问题解决的效率
- 新用户的上手速度
- 开发者的满意度

## 📊 成功标准

### 文档体系被认为成功当：
- ✅ 新用户能在15分钟内完成第一个视频生成
- ✅ 新开发者能在1小时内理解项目架构
- ✅ 常见问题能在文档中快速找到答案
- ✅ 文档维护工作量保持在合理范围
- ✅ 信息查找不需要跨越多个文档

## 🎯 实施建议

### 对AI助手
1. 严格按照文档结构查找和提供信息
2. 引用文档时使用正确的路径和章节
3. 发现文档问题时及时建议修正
4. 协助维护文档的一致性和准确性

### 对开发团队
1. 将文档规范纳入代码审查流程
2. 建立文档更新的责任制
3. 定期培训团队文档使用规范
4. 持续收集和处理文档反馈


## 🎯 配置文件使用规范

### examples/ 目录配置文件标准

#### 核心测试配置 (已定义)
项目中已准备了两个标准测试配置文件，**严禁随意创建新的测试配置**：

##### 1. `real_estate_showcase_simple.json` - 快速验证版本
- **用途**: 功能开发和快速验证测试
- **特点**: 
  - 内容简化：3个场景，总时长约14秒
  - 生成速度快：适合频繁测试
  - 配置完整：包含所有核心功能验证
  - 质量设置：standard (720p)
- **使用场景**:
  - 开发过程中的功能验证
  - 代码修改后的快速测试
  - 问题排查和调试
  - 性能基准测试

##### 2. `real_estate_showcase.json` - 完整效果版本
- **用途**: 最终效果验证和完整功能测试
- **特点**:
  - 内容完整：10个场景，总时长约56秒
  - 功能齐全：包含intro、outro、overlays等所有特性
  - 高质量输出：完整的视觉效果展示
  - 质量设置：standard (720p)
- **使用场景**:
  - 版本发布前的最终验证
  - 完整功能演示
  - 客户展示和项目汇报
  - 架构变更后的全面测试

#### 配置文件使用原则

##### ✅ 正确的测试流程
```bash
# 1. 开发阶段 - 使用简化版本进行快速验证
python main.py generate examples/real_estate_showcase_simple.json

# 2. 功能完成后 - 使用完整版本进行全面测试
python main.py generate examples/real_estate_showcase.json
```

##### ❌ 严禁的做法
- **禁止创建新的测试配置文件**
- **禁止修改现有测试配置的核心结构**
- **禁止为临时测试创建专门的配置文件**

##### ⚠️ 例外情况
仅在以下情况下可以考虑创建新配置：
- 新功能需要专门的配置结构验证
- 性能压力测试需要特殊配置
- 用户案例和演示需要特定场景
- **必须经过项目负责人审批**

#### 配置文件维护规范

##### 1. 内容更新规则
- **简化版本**: 保持3个场景的基本结构，更新URL和测试内容
- **完整版本**: 保持10个场景的完整结构，确保所有功能的全面测试
- **同步更新**: 两个版本的基础配置（basic部分）必须保持一致

##### 2. 质量控制
- **配置验证**: 每次修改后必须通过配置验证测试
- **生成测试**: 确保两个版本都能正常生成视频
- **功能覆盖**: 验证所有核心功能在两个版本中都能正常工作

##### 3. 文档同步
- 配置文件修改后，同步更新相关文档
- 保持示例配置与文档说明的一致性
- 及时更新配置文件的变更说明

### 文件管理规范 ⚠️ 重要
**严禁随意创建新文件！** 这是导致项目结构混乱的主要原因。

#### 文件创建原则
- **优先修改现有文件**: 在现有文件中添加功能，而不是创建新文件
- **必要性验证**: 创建新文件前必须验证确实需要新的模块
- **架构一致性**: 新文件必须符合既定的分层架构
- **命名规范**: 遵循项目的命名约定

#### 禁止的做法
```python
# ❌ 错误做法 - 随意创建新文件
# simplified_animation_system.py
# debug_animation_application.py
# test_animation_final.json
# animation_fix_final_report.md
# quick_test_config.json  # 禁止创建临时测试配置
```

#### 正确的做法
```python
# ✅ 正确做法 - 在现有文件中扩展功能
# enhanced_animation_renderer.py (修改现有文件)
# template_renderer.py (在现有文件中添加方法)
# examples/real_estate_showcase.json (更新现有配置)
# examples/real_estate_showcase_simple.json (更新现有配置)
```

#### 文件清理规则
- **立即清理**: 开发过程中产生的临时文件必须立即清理
- **测试文件**: 测试完成后立即删除临时测试文件
- **文档整合**: 将分散的文档整合到主文档中
- **定期审查**: 定期检查并清理不必要的文件

## 🏗️ 项目架构规范 ✅ (已完成重构)

### 分层架构设计
项目已成功实现严格的四层架构设计：

#### 表现层 (Presentation) - `src/presentation/`
- **`main.py`** - 命令行接口，处理用户交互

#### 应用层 (Application) - `src/application/`
- **`video_generator.py`** - 视频生成服务，整合所有组件
- **`config.py`** - 配置处理服务，解析和验证配置文件
- **`resources.py`** - 资源管理服务，处理文件缓存和下载

#### 领域层 (Domain) - `src/domain/`
- **`renderer.py`** - 模板渲染核心，处理视频合成和字幕
- **`animation_engine.py`** - 动画引擎，统一处理所有动画效果

#### 基础设施层 (Infrastructure) - `src/infrastructure/`
- **`validator.py`** - 配置验证，确保数据完整性

### 架构优势
- **清晰的职责分离** - 每层专注特定功能
- **易于维护扩展** - 模块化设计支持独立开发
- **符合最佳实践** - 遵循软件工程标准
- **代码复用性高** - 统一的接口设计

### 文件组织原则
- 每个层级职责明确，不得跨层调用
- 优先修改现有文件，避免创建新文件
- 工具脚本放在 tools/ 目录
- 配置文件放在 configs/ 目录
- 缓存、输出、临时目录在项目根目录

---

**📅 最后更新**: 2025-01-08
**📊 规范版本**: v1.2 (架构重构完成)
**🎯 适用范围**: Reavo Video Creator 项目全体成员

---

**遵循此规范，确保文档体系的高质量和持续改进！** 📚✨
